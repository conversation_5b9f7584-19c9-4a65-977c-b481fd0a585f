/**
 * This file will automatically be loaded by vite and run in the "renderer" context.
 * To learn more about the differences between the "main" and the "renderer" context in
 * Electron, visit:
 *
 * https://electronjs.org/docs/tutorial/process-model
 *
 * By default, Node.js integration in this file is disabled. When enabling Node.js integration
 * in a renderer process, please be aware of potential security implications. You can read
 * more about security risks here:
 *
 * https://electronjs.org/docs/tutorial/security
 *
 * To enable Node.js integration in this file, open up `main.ts` and enable the `nodeIntegration`
 * flag:
 *
 * ```
 *  // Create the browser window.
 *  mainWindow = new BrowserWindow({
 *    width: 800,
 *    height: 600,
 *    webPreferences: {
 *      nodeIntegration: true
 *    }
 *  });
 * ```
 */

import "./styles/global.css";
import "./i18n";
import type React from "react";
import {StrictMode, useEffect, useState} from "react";
import ReactDOM from "react-dom/client";
import {BrowserRouter, Navigate, Route, Routes} from "react-router";
import Login from "./pages/Login";
import MainLayout from "./pages/MainLayout";
import Register from "./pages/Register";
import {autoLoginSafe} from "./services/auth";
import {useAtomValue} from "jotai";
import {userAtom} from "./jotai/atoms/user";

// 受保护的路由组件
const ProtectedRoute: React.FC<{
	children: React.ReactNode;
}> = ({children}) => {
	const user = useAtomValue(userAtom);

	return user ? children : <Navigate to="/login" replace />;
};

const App = () => {
	const [isInitialized, setIsInitialized] = useState(false);

	useEffect(() => {
		const initializeApp = async () => {
			// 应用启动时恢复认证状态
			// restoreUserState();

			// 尝试自动登录
			await autoLoginSafe();

			setIsInitialized(true);
		};

		initializeApp();
	}, []);

	// 显示加载状态
	if (!isInitialized) {
		return (
			<div className="h-screen flex items-center justify-center bg-gray-100">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p className="text-gray-600">正在启动应用...</p>
				</div>
			</div>
		);
	}

	return (
		<BrowserRouter>
			<Routes>
				<Route path="/login" element={<Login />} />
				<Route path="/register" element={<Register />} />
				<Route
					path="/"
					element={
						<ProtectedRoute>
							<MainLayout />
						</ProtectedRoute>
					}
				/>
			</Routes>
		</BrowserRouter>
	);
};

const rootElement = document.querySelector<HTMLDivElement>("#root");
if (!rootElement) {
	throw new Error("Root element not found");
}

ReactDOM.createRoot(rootElement).render(
	<StrictMode>
		<App />
	</StrictMode>,
);
