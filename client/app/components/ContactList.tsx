import type React from "react";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import type {Contact} from "../services/database";
import {Button} from "./ui/button";
import {Input} from "./ui/input";
import {useAtomValue} from "jotai";
import {dbAtom} from "@/jotai/atoms/db";

// 分组标题组件
const SectionTitle: React.FC<{title: string; count: number}> = ({
	title,
	count,
}) => (
	<div className="px-3 py-2 bg-gray-50 border-b">
		<h4 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
			{title} ({count})
		</h4>
	</div>
);

// 联系人项组件
const ContactItem: React.FC<{
	contact: Contact;
	getStatusColor: (status: Contact["status"]) => string;
	formatLastSeen: (timestamp?: number) => string;
	t: (key: string) => string;
}> = ({contact, getStatusColor, formatLastSeen, t}) => (
	<div className="p-3 cursor-pointer transition-colors border-b border-gray-100 hover:bg-gray-50">
		<div className="flex items-center space-x-3">
			{/* 头像和状态 */}
			<div className="relative">
				<div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
					{contact.avatar ? (
						<img
							src={contact.avatar}
							alt={contact.nickname}
							className="w-12 h-12 rounded-full object-cover"
						/>
					) : (
						<span className="text-gray-600 font-medium">
							{contact.nickname.charAt(0).toUpperCase()}
						</span>
					)}
				</div>
				{/* 状态指示器 */}
				<div
					className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(contact.status)}`}
				/>
			</div>

			{/* 联系人信息 */}
			<div className="flex-1 min-w-0">
				<div className="flex items-center justify-between">
					<h3 className="font-medium text-sm truncate">{contact.nickname}</h3>
					<span className="text-xs text-gray-500">
						{contact.status === "online"
							? t("contact.online")
							: formatLastSeen(contact.lastSeen)}
					</span>
				</div>
				<div className="flex items-center justify-between mt-1">
					<p className="text-xs text-gray-600 truncate">@{contact.account}</p>
					<div className="flex space-x-1">
						{/* 发消息按钮 */}
						<Button
							variant="ghost"
							size="sm"
							className="h-6 px-2 text-xs"
							onClick={(e) => {
								e.stopPropagation();
								// TODO: 开始聊天
							}}
						>
							<svg
								className="w-3 h-3"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								role="img"
							>
								<title>Send Message Icon</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
								/>
							</svg>
						</Button>
					</div>
				</div>
			</div>
		</div>
	</div>
);

const ContactList: React.FC = () => {
	const {t} = useTranslation();
	const [contacts, setContacts] = useState<Contact[]>([]);
	const [searchQuery, setSearchQuery] = useState("");
	const [loading, setLoading] = useState(true);
	const db = useAtomValue(dbAtom);

	// 加载联系人列表
	useEffect(() => {
		const loadContacts = async () => {
			try {
				if (!db) {
					setLoading(false);
					return;
				}

				// 获取所有好友联系人
				const allContacts = await db.getContacts();
				setContacts(allContacts);
			} catch (error) {
				console.error("加载联系人列表失败:", error);
			} finally {
				setLoading(false);
			}
		};

		loadContacts();
	}, []);

	// 过滤联系人列表
	const filteredContacts = contacts.filter(
		(contact) =>
			contact.nickname.toLowerCase().includes(searchQuery.toLowerCase()) ||
			contact.account.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	// 按状态分组
	const onlineContacts = filteredContacts.filter(
		(contact) => contact.status === "online",
	);
	const offlineContacts = filteredContacts.filter(
		(contact) => contact.status !== "online",
	);

	// 格式化最后在线时间
	const formatLastSeen = (timestamp?: number) => {
		if (!timestamp) return t("contact.neverOnline");

		const now = Date.now();
		const diff = now - timestamp;

		if (diff < 60 * 1000) {
			return t("contact.justNow");
		} else if (diff < 60 * 60 * 1000) {
			const minutes = Math.floor(diff / (60 * 1000));
			return t("contact.minutesAgo", {count: minutes});
		} else if (diff < 24 * 60 * 60 * 1000) {
			const hours = Math.floor(diff / (60 * 60 * 1000));
			return t("contact.hoursAgo", {count: hours});
		} else {
			const days = Math.floor(diff / (24 * 60 * 60 * 1000));
			return t("contact.daysAgo", {count: days});
		}
	};

	// 获取状态颜色
	const getStatusColor = (status: Contact["status"]) => {
		switch (status) {
			case "online":
				return "bg-green-500";
			case "away":
				return "bg-yellow-500";
			case "busy":
				return "bg-red-500";
			default:
				return "bg-gray-400";
		}
	};

	if (loading) {
		return (
			<div className="flex-1 flex items-center justify-center">
				<div className="text-center text-muted-foreground">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm">{t("common.loading")}</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex-1 flex flex-col">
			{/* 搜索框 */}
			<div className="p-3 border-b">
				<Input
					type="text"
					placeholder={t("contact.searchContacts")}
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					className="w-full"
				/>
			</div>

			{/* 联系人列表 */}
			<div className="flex-1 overflow-y-auto">
				{filteredContacts.length === 0 ? (
					<div className="flex-1 flex items-center justify-center p-8">
						<div className="text-center text-muted-foreground">
							<svg
								className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								role="img"
							>
								<title>No Contacts Icon</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
								/>
							</svg>
							<p className="text-sm font-medium mb-1">
								{searchQuery
									? t("contact.noSearchResults")
									: t("contact.noContactsYet")}
							</p>
							<p className="text-xs">
								{searchQuery
									? t("contact.tryDifferentKeywords")
									: t("contact.addFirstContact")}
							</p>
						</div>
					</div>
				) : (
					<div>
						{/* 在线联系人 */}
						{onlineContacts.length > 0 && (
							<div>
								<SectionTitle
									title={t("contact.onlineContacts")}
									count={onlineContacts.length}
								/>
								{onlineContacts.map((contact) => (
									<ContactItem
										key={contact.userId}
										contact={contact}
										getStatusColor={getStatusColor}
										formatLastSeen={formatLastSeen}
										t={t}
									/>
								))}
							</div>
						)}

						{/* 离线联系人 */}
						{offlineContacts.length > 0 && (
							<div>
								<SectionTitle
									title={t("contact.offlineContacts")}
									count={offlineContacts.length}
								/>
								{offlineContacts.map((contact) => (
									<ContactItem
										key={contact.userId}
										contact={contact}
										getStatusColor={getStatusColor}
										formatLastSeen={formatLastSeen}
										t={t}
									/>
								))}
							</div>
						)}
					</div>
				)}
			</div>

			{/* 添加联系人按钮 */}
			<div className="p-3 border-t">
				<Button className="w-full" size="sm">
					<svg
						className="w-4 h-4 mr-2"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Add Contact Icon</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 4v16m8-8H4"
						/>
					</svg>
					{t("contact.addContact")}
				</Button>
			</div>
		</div>
	);
};

export default ContactList;
