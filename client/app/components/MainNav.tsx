import {MessageCircle, UserRound} from "lucide-react";
import {Button} from "./ui/button";
import {useRef} from "react";
import {useLocation, useNavigate} from "react-router";

const MainNav = () => {
	const location = useLocation();
	const navigate = useNavigate();

	// 用于判断当前导航是否激活
	const currentPath = location.pathname;

	const navs = useRef([
		{
			name: "chats",
			icon: <MessageCircle />,
			onClick: () => {
				navigate("/chats");
			},
		},
		{
			name: "contacts",
			icon: <UserRound />,
			onClick: () => {
				navigate("/contacts");
			},
		},
	]);

	return (
		<div
			id="main-nav"
			className="w-12 h-screen overflow-hidden flex flex-col items-center py-2"
		>
			{navs.current.map(({name, icon, onClick}) => (
				<Button
					key={name}
					variant="secondary"
					size="icon"
					className="size-8"
					onClick={onClick}
				>
					{icon}
				</Button>
			))}
		</div>
	);
};

export default MainNav;
