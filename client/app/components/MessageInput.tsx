import type React from "react";
import {useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import {Button} from "./ui/button";
import {Input} from "./ui/input";

interface MessageInputProps {
	onSendMessage: (
		content: string,
		type: "text" | "image" | "file" | "audio" | "video",
	) => void;
	disabled?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
	onSendMessage,
	disabled = false,
}) => {
	const {t} = useTranslation();
	const [messageInput, setMessageInput] = useState("");
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const imageInputRef = useRef<HTMLInputElement>(null);

	// 发送文本消息
	const handleSendText = (e: React.FormEvent) => {
		e.preventDefault();

		if (!messageInput.trim() || disabled || isUploading) {
			return;
		}

		onSendMessage(messageInput.trim(), "text");
		setMessageInput("");
	};

	// 处理文件上传
	const handleFileUpload = async (
		files: FileList | null,
		type: "image" | "file",
	) => {
		if (!files || files.length === 0) return;

		const file = files[0];
		setIsUploading(true);

		try {
			// TODO: 实际上传文件到服务器
			// 这里模拟上传过程
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// 创建临时URL用于预览
			const fileUrl = URL.createObjectURL(file);
			onSendMessage(fileUrl, type);
		} catch (error) {
			console.error("文件上传失败:", error);
			// TODO: 显示错误提示
		} finally {
			setIsUploading(false);
		}
	};

	// 处理图片选择
	const handleImageSelect = () => {
		imageInputRef.current?.click();
	};

	// 处理文件选择
	const handleFileSelect = () => {
		fileInputRef.current?.click();
	};

	// 处理键盘事件
	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSendText(e);
		}
	};

	return (
		<div className="p-4 border-t bg-white">
			<form onSubmit={handleSendText} className="flex items-end space-x-2">
				{/* 附件按钮 */}
				<div className="flex space-x-1">
					{/* 图片按钮 */}
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={handleImageSelect}
						disabled={disabled || isUploading}
						className="p-2"
					>
						<svg
							className="w-5 h-5"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
						>
							<title>Send Image</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
							/>
						</svg>
					</Button>

					{/* 文件按钮 */}
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={handleFileSelect}
						disabled={disabled || isUploading}
						className="p-2"
					>
						<svg
							className="w-5 h-5"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
						>
							<title>Send File</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
							/>
						</svg>
					</Button>
				</div>

				{/* 消息输入框 */}
				<div className="flex-1">
					<Input
						type="text"
						placeholder={
							isUploading ? t("chat.uploading") : t("chat.typeMessage")
						}
						value={messageInput}
						onChange={(e) => setMessageInput(e.target.value)}
						onKeyDown={handleKeyDown}
						disabled={disabled || isUploading}
						className="resize-none"
					/>
				</div>

				{/* 发送按钮 */}
				<Button
					type="submit"
					disabled={!messageInput.trim() || disabled || isUploading}
					size="sm"
				>
					{isUploading ? (
						<svg
							className="w-4 h-4 animate-spin"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
						>
							<title>Uploading</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
							/>
						</svg>
					) : (
						<svg
							className="w-4 h-4"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
						>
							<title>Send</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
							/>
						</svg>
					)}
					<span className="ml-2">{t("chat.send")}</span>
				</Button>
			</form>

			{/* 隐藏的文件输入 */}
			<input
				ref={imageInputRef}
				type="file"
				accept="image/*"
				onChange={(e) => handleFileUpload(e.target.files, "image")}
				className="hidden"
			/>
			<input
				ref={fileInputRef}
				type="file"
				onChange={(e) => handleFileUpload(e.target.files, "file")}
				className="hidden"
			/>
		</div>
	);
};

export default MessageInput;
