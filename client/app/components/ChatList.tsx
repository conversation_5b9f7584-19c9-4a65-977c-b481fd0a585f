import type React from "react";
import {useEffect, useState} from "react";
import {useTranslation} from "react-i18next";
import type {Chat} from "../services/database";
import {Button} from "./ui/button";
import {Input} from "./ui/input";
import {useAtomValue} from "jotai";
import {dbAtom} from "@/jotai/atoms/db";

interface ChatListProps {
	selectedChatId: string | null;
	onChatSelect: (chatId: string) => void;
}

// 分组标题组件
const SectionTitle: React.FC<{title: string; count: number}> = ({
	title,
	count,
}) => (
	<div className="px-3 py-2 bg-gray-50 border-b">
		<h4 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
			{title} ({count})
		</h4>
	</div>
);

// 聊天项组件
const ChatItem: React.FC<{
	chat: Chat;
	selectedChatId: string | null;
	onChatSelect: (chatId: string) => void;
	formatTime: (timestamp?: number) => string;
	t: (key: string) => string;
}> = ({chat, selectedChatId, onChatSelect, formatTime, t}) => (
	<button
		type="button"
		onClick={() => onChatSelect(chat.chatId)}
		onKeyDown={(e) => {
			if (e.key === "Enter" || e.key === " ") {
				e.preventDefault();
				onChatSelect(chat.chatId);
			}
		}}
		className={`w-full p-3 text-left transition-colors border-b border-gray-100 hover:bg-gray-50 ${
			selectedChatId === chat.chatId ? "bg-primary/10 border-primary/20" : ""
		}`}
	>
		<div className="flex items-center space-x-3">
			{/* 头像 */}
			<div className="relative">
				<div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
					{chat.avatar ? (
						<img
							src={chat.avatar}
							alt={chat.name}
							className="w-12 h-12 rounded-full object-cover"
						/>
					) : (
						<span className="text-gray-600 font-medium">
							{chat.name.charAt(0).toUpperCase()}
						</span>
					)}
				</div>
				{chat.type === "group" && (
					<div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
						<svg
							className="w-2 h-2 text-white"
							fill="currentColor"
							viewBox="0 0 20 20"
							role="img"
						>
							<title>Group Chat Icon</title>
							<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
						</svg>
					</div>
				)}
			</div>

			{/* 聊天信息 */}
			<div className="flex-1 min-w-0">
				<div className="flex items-center justify-between">
					<h3 className="font-medium text-sm truncate">
						{chat.name}
						{chat.isPinned && (
							<svg
								className="inline w-3 h-3 ml-1 text-yellow-500"
								fill="currentColor"
								viewBox="0 0 20 20"
								role="img"
							>
								<title>Pinned Chat Icon</title>
								<path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
							</svg>
						)}
					</h3>
					<span className="text-xs text-gray-500">
						{formatTime(chat.lastMessageTime)}
					</span>
				</div>
				<div className="flex items-center justify-between mt-1">
					<p className="text-xs text-gray-600 truncate">
						{chat.lastMessage || t("chat.noMessages")}
					</p>
					{chat.unreadCount > 0 && (
						<span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[18px] text-center">
							{chat.unreadCount > 99 ? "99+" : chat.unreadCount}
						</span>
					)}
				</div>
			</div>
		</div>
	</button>
);

const ChatList: React.FC<ChatListProps> = ({selectedChatId, onChatSelect}) => {
	const {t} = useTranslation();
	const [chats, setChats] = useState<Chat[]>([]);
	const [searchQuery, setSearchQuery] = useState("");
	const [loading, setLoading] = useState(true);
	const db = useAtomValue(dbAtom);

	// 加载聊天列表
	useEffect(() => {
		const loadChats = async () => {
			try {
				if (!db) {
					setLoading(false);
					return;
				}

				// 获取所有聊天，按最后消息时间排序
				const allChats = await db.chats
					.orderBy("lastMessageTime")
					.reverse()
					.toArray();

				setChats(allChats);
			} catch (error) {
				console.error("加载聊天列表失败:", error);
			} finally {
				setLoading(false);
			}
		};

		loadChats();
	}, []);

	// 过滤聊天列表
	const filteredChats = chats.filter((chat) =>
		chat.name.toLowerCase().includes(searchQuery.toLowerCase()),
	);

	// 分组聊天
	const recentChats = filteredChats.filter(
		(chat) => chat.isPinned || chat.lastMessageTime,
	);
	const privateChats = filteredChats.filter(
		(chat) => chat.type === "private" && !chat.isPinned,
	);
	const groupChats = filteredChats.filter(
		(chat) => chat.type === "group" && !chat.isPinned,
	);

	// 格式化时间
	const formatTime = (timestamp?: number) => {
		if (!timestamp) return "";

		const now = Date.now();
		const diff = now - timestamp;
		const date = new Date(timestamp);

		if (diff < 24 * 60 * 60 * 1000) {
			// 今天
			return date.toLocaleTimeString("zh-CN", {
				hour: "2-digit",
				minute: "2-digit",
			});
		} else if (diff < 7 * 24 * 60 * 60 * 1000) {
			// 本周
			return date.toLocaleDateString("zh-CN", {weekday: "short"});
		} else {
			// 更早
			return date.toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
			});
		}
	};

	if (loading) {
		return (
			<div className="flex-1 flex items-center justify-center">
				<div className="text-center text-muted-foreground">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm">{t("common.loading")}</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex-1 flex flex-col">
			{/* 搜索框 */}
			<div className="p-3 border-b">
				<Input
					type="text"
					placeholder={t("chat.searchChats")}
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
					className="w-full"
				/>
			</div>

			{/* 聊天列表 */}
			<div className="flex-1 overflow-y-auto">
				{filteredChats.length === 0 ? (
					<div className="flex-1 flex items-center justify-center p-8">
						<div className="text-center text-muted-foreground">
							<svg
								className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								role="img"
							>
								<title>No Chats Icon</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
								/>
							</svg>
							<p className="text-sm font-medium mb-1">
								{searchQuery ? t("chat.noSearchResults") : t("chat.noChatsYet")}
							</p>
							<p className="text-xs">
								{searchQuery
									? t("chat.tryDifferentKeywords")
									: t("chat.startNewChat")}
							</p>
						</div>
					</div>
				) : (
					<div>
						{/* 最近聊天 */}
						{recentChats.length > 0 && (
							<div>
								<SectionTitle
									title={t("chat.recentChats")}
									count={recentChats.length}
								/>
								{recentChats.map((chat) => (
									<ChatItem
										key={chat.chatId}
										chat={chat}
										selectedChatId={selectedChatId}
										onChatSelect={onChatSelect}
										formatTime={formatTime}
										t={t}
									/>
								))}
							</div>
						)}

						{/* 单人聊天 */}
						{privateChats.length > 0 && (
							<div>
								<SectionTitle
									title={t("chat.privateChats")}
									count={privateChats.length}
								/>
								{privateChats.map((chat) => (
									<ChatItem
										key={chat.chatId}
										chat={chat}
										selectedChatId={selectedChatId}
										onChatSelect={onChatSelect}
										formatTime={formatTime}
										t={t}
									/>
								))}
							</div>
						)}

						{/* 群组聊天 */}
						{groupChats.length > 0 && (
							<div>
								<SectionTitle
									title={t("chat.groupChats")}
									count={groupChats.length}
								/>
								{groupChats.map((chat) => (
									<ChatItem
										key={chat.chatId}
										chat={chat}
										selectedChatId={selectedChatId}
										onChatSelect={onChatSelect}
										formatTime={formatTime}
										t={t}
									/>
								))}
							</div>
						)}
					</div>
				)}
			</div>

			{/* 新建聊天按钮 */}
			<div className="p-3 border-t">
				<Button className="w-full" size="sm">
					<svg
						className="w-4 h-4 mr-2"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Add Icon</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 4v16m8-8H4"
						/>
					</svg>
					{t("chat.newChat")}
				</Button>
			</div>
		</div>
	);
};

export default ChatList;
