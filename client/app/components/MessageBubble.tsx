import type React from "react";
import {useTranslation} from "react-i18next";
import type {Message} from "../services/database";
import {useAtomValue} from "jotai";
import {userAtom} from "@/jotai/atoms/user";

interface MessageBubbleProps {
	message: Message;
	showAvatar?: boolean;
	showTime?: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
	message,
	showAvatar = true,
	showTime = true,
}) => {
	const {t} = useTranslation();
	const user = useAtomValue(userAtom);
	const isOwnMessage = message.sender === user?.account;

	// 格式化时间
	const formatTime = (timestamp: number) => {
		const date = new Date(timestamp);
		return date.toLocaleTimeString("zh-CN", {
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	// 获取状态图标
	const getStatusIcon = () => {
		switch (message.status) {
			case "sending":
				return (
					<svg
						className="w-3 h-3 animate-spin"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Sending</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
						/>
					</svg>
				);
			case "sent":
				return (
					<svg
						className="w-3 h-3"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Sent</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M5 13l4 4L19 7"
						/>
					</svg>
				);
			case "delivered":
				return (
					<svg
						className="w-3 h-3"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Delivered</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M5 13l4 4L19 7"
						/>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M9 13l4 4L23 7"
						/>
					</svg>
				);
			case "read":
				return (
					<svg
						className="w-3 h-3 text-blue-500"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Read</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M5 13l4 4L19 7"
						/>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M9 13l4 4L23 7"
						/>
					</svg>
				);
			case "failed":
				return (
					<svg
						className="w-3 h-3 text-red-500"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						role="img"
					>
						<title>Failed</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				);
			default:
				return null;
		}
	};

	// 渲染消息内容
	const renderMessageContent = () => {
		if (message.isDeleted) {
			return (
				<div className="italic text-gray-500 text-sm">
					{t("chat.messageRecalled")}
				</div>
			);
		}

		switch (message.type) {
			case "text":
				return (
					<div className="whitespace-pre-wrap break-words">
						{message.content}
					</div>
				);
			case "image":
				return (
					<div className="max-w-xs">
						<button
							type="button"
							className="block"
							onClick={() => {
								// TODO: 打开图片预览
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.preventDefault();
									// TODO: 打开图片预览
								}
							}}
						>
							<img
								src={message.content}
								alt="图片消息"
								className="rounded-lg max-w-full h-auto hover:opacity-90 transition-opacity"
							/>
						</button>
					</div>
				);
			case "file":
				return (
					<div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg max-w-xs">
						<svg
							className="w-8 h-8 text-gray-500"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
						>
							<title>File</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
							/>
						</svg>
						<div className="flex-1 min-w-0">
							<p className="text-sm font-medium truncate">
								{message.content.split("/").pop() || "文件"}
							</p>
							<p className="text-xs text-gray-500">
								{t("chat.clickToDownload")}
							</p>
						</div>
					</div>
				);
			case "audio":
				return (
					<div className="flex items-center space-x-3 p-3 bg-gray-100 rounded-lg max-w-xs">
						<button
							type="button"
							className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
						>
							<svg
								className="w-4 h-4"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								role="img"
							>
								<title>Play Audio</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15a2 2 0 002-2V9a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293L10.293 4.293A1 1 0 009.586 4H8a2 2 0 00-2 2v5a2 2 0 002 2z"
								/>
							</svg>
						</button>
						<div className="flex-1">
							<div className="w-full bg-gray-300 rounded-full h-1">
								<div
									className="bg-blue-500 h-1 rounded-full"
									style={{width: "0%"}}
								></div>
							</div>
							<p className="text-xs text-gray-500 mt-1">
								{t("chat.audioMessage")}
							</p>
						</div>
					</div>
				);
			case "video":
				return (
					<div className="max-w-xs">
						<div className="relative">
							<video
								src={message.content}
								className="rounded-lg max-w-full h-auto"
								controls
								preload="metadata"
							>
								<track kind="captions" src="" label="No captions available" />
							</video>
							<div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
								<svg
									className="w-12 h-12 text-white"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
									role="img"
								>
									<title>Play Video</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15a2 2 0 002-2V9a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293L10.293 4.293A1 1 0 009.586 4H8a2 2 0 00-2 2v5a2 2 0 002 2z"
									/>
								</svg>
							</div>
						</div>
					</div>
				);
			case "system":
				return (
					<div className="text-center text-gray-500 text-sm bg-gray-100 rounded-full px-4 py-2">
						{message.content}
					</div>
				);
			default:
				return (
					<div className="text-gray-500 text-sm">
						{t("chat.unsupportedMessageType")}
					</div>
				);
		}
	};

	// 系统消息特殊处理
	if (message.type === "system") {
		return (
			<div className="flex justify-center my-4">{renderMessageContent()}</div>
		);
	}

	return (
		<div
			className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} mb-4`}
		>
			<div
				className={`flex max-w-[70%] ${isOwnMessage ? "flex-row-reverse" : "flex-row"}`}
			>
				{/* 头像 */}
				{showAvatar && !isOwnMessage && (
					<div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
						<span className="text-gray-600 text-xs font-medium">
							{message.senderNickname?.charAt(0) ||
								message.sender.charAt(0).toUpperCase()}
						</span>
					</div>
				)}

				{/* 消息内容 */}
				<div
					className={`flex flex-col ${isOwnMessage ? "items-end" : "items-start"}`}
				>
					{/* 发送者昵称（非自己的消息） */}
					{!isOwnMessage && message.senderNickname && (
						<div className="text-xs text-gray-500 mb-1 px-1">
							{message.senderNickname}
						</div>
					)}

					{/* 消息气泡 */}
					<div
						className={`relative px-4 py-2 rounded-2xl ${
							isOwnMessage
								? "bg-blue-500 text-white rounded-br-md"
								: "bg-gray-200 text-gray-900 rounded-bl-md"
						} ${message.type === "text" ? "max-w-none" : ""}`}
					>
						{renderMessageContent()}

						{/* 回复消息指示 */}
						{message.replyTo && (
							<div className="text-xs opacity-75 mt-1 pt-1 border-t border-current border-opacity-20">
								{t("chat.replyTo")}: {message.replyTo}
							</div>
						)}
					</div>

					{/* 时间和状态 */}
					{showTime && (
						<div
							className={`flex items-center space-x-1 mt-1 px-1 ${isOwnMessage ? "flex-row-reverse" : "flex-row"}`}
						>
							<span className="text-xs text-gray-500">
								{formatTime(message.timestamp)}
							</span>
							{isOwnMessage && (
								<div className="text-gray-500">{getStatusIcon()}</div>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default MessageBubble;
