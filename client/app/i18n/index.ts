import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import {initReactI18next} from "react-i18next";
import zhCN from "./zhCN";

i18n
	.use(LanguageDetector)
	.use(initReactI18next)
	.init({
		resources: {
			"zh-CN": zhCN,
		},
		lng: "zh-CN", // 默认语言
		fallbackLng: "zh-CN",
		debug: false,
		interpolation: {
			escapeValue: false,
		},
		detection: {
			order: ["localStorage", "navigator"],
			caches: ["localStorage"],
		},
	});
