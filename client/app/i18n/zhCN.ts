// 简体中文翻译
const zhCN = {
	translation: {
		// 通用
		common: {
			confirm: "确认",
			cancel: "取消",
			save: "保存",
			delete: "删除",
			edit: "编辑",
			loading: "加载中...",
			error: "错误",
			success: "成功",
			warning: "警告",
			info: "信息",
			close: "关闭",
			back: "返回",
			next: "下一步",
			previous: "上一步",
			submit: "提交",
			reset: "重置",
			search: "搜索",
			clear: "清空",
			refresh: "刷新",
			retry: "重试",
			copy: "复制",
			paste: "粘贴",
			cut: "剪切",
			select: "选择",
			selectAll: "全选",
			deselect: "取消选择",
			expand: "展开",
			collapse: "收起",
			more: "更多",
			less: "收起",
			settings: "设置",
			help: "帮助",
			about: "关于",
			version: "版本",
			update: "更新",
			download: "下载",
			upload: "上传",
			import: "导入",
			export: "导出",
			print: "打印",
			share: "分享",
			send: "发送",
			receive: "接收",
			connect: "连接",
			disconnect: "断开连接",
			online: "在线",
			offline: "离线",
			connecting: "连接中...",
			connected: "已连接",
			disconnected: "已断开",
			reconnecting: "重新连接中...",
			yes: "是",
			no: "否",
			ok: "确定",
			apply: "应用",
			preview: "预览",
			fullscreen: "全屏",
			minimize: "最小化",
			maximize: "最大化",
			restore: "还原",
			// 表单字段
			serverUrl: "服务器地址",
			serverUrlPlaceholder: "请输入服务器地址",
			account: "账号",
			accountPlaceholder: "请输入账号",
			password: "密码",
			passwordPlaceholder: "请输入密码",
			sending: "发送中...",
		},

		// 联系人相关
		contact: {
			searchContacts: "搜索联系人",
			noSearchResults: "没有找到相关联系人",
			noContactsYet: "还没有联系人",
			tryDifferentKeywords: "尝试使用不同的关键词",
			addFirstContact: "添加第一个联系人",
			onlineContacts: "在线联系人",
			offlineContacts: "离线联系人",
			addContact: "添加联系人",
			online: "在线",
			neverOnline: "从未在线",
			justNow: "刚刚",
			minutesAgo: "{{count}}分钟前",
			hoursAgo: "{{count}}小时前",
			daysAgo: "{{count}}天前",
		},

		// 应用相关
		app: {
			name: "Liyu 即时通讯",
			title: "Liyu",
			logo: "应用图标",
			description: "安全、快速的即时通讯应用",
			starting: "正在启动应用...",
			initializing: "正在初始化...",
			ready: "应用已就绪",
		},

		// 认证相关
		auth: {
			login: "登录",
			register: "注册",
			logout: "退出登录",
			loggingIn: "登录中...",
			registering: "注册中...",
			loginSuccess: "登录成功",
			registerSuccess: "注册成功",
			loginFailed: "登录失败",
			registerFailed: "注册失败",
			logoutConfirm: "确定要退出登录吗？",
			invalidCredentials: "账号或密码错误",
			accountExists: "账号已存在",
			passwordMismatch: "两次输入的密码不一致",
			passwordTooShort: "密码长度至少6位",
			accountTooShort: "账号长度至少3位",
			networkError: "网络连接失败，请检查服务器地址",
			serverError: "服务器错误，请稍后重试",
		},

		// 登录页面
		login: {
			title: "登录",
			subtitle: "欢迎回来",
			description: "请输入您的账号信息",
			submit: "登录",
			rememberPassword: "记住密码",
			autoLogin: "自动登录",
			startupOnBoot: "开机启动",
			noAccount: "没有账号？",
			selectUser: "选择用户",
			newUser: "新用户",
			quickLogin: "快速登录",
			deleteUser: "删除用户",
			orLoginWith: "或使用新账号登录",
		},

		// 注册页面
		register: {
			title: "注册",
			subtitle: "创建新账号",
			description: "请填写注册信息",
			submit: "注册",
			confirmPassword: "确认密码",
			confirmPasswordPlaceholder: "请再次输入密码",
			hasAccount: "已有账号？",
		},

		// 导航相关
		nav: {
			chats: "聊天",
			contacts: "联系人",
		},

		// 聊天页面
		chat: {
			title: "聊天",
			selectChat: "选择聊天",
			typeMessage: "输入消息...",
			sendMessage: "发送消息",
			sending: "发送中...",
			messageSent: "消息已发送",
			messageFailed: "消息发送失败",
			send: "发送",
			userNotFound: "用户信息未找到",
			selectChatToStart: "选择聊天开始对话",
			selectChatDescription: "从左侧选择一个聊天或联系人开始对话",
			messageRecalled: "消息已撤回",
			searchChats: "搜索聊天",
			noMessages: "暂无消息",
			noSearchResults: "没有找到相关聊天",
			noChatsYet: "还没有聊天记录",
			tryDifferentKeywords: "尝试使用不同的关键词",
			startNewChat: "开始新的聊天",
			recentChats: "最近聊天",
			privateChats: "单人聊天",
			groupChats: "群组聊天",
			newChat: "新建聊天",
			unknownChat: "未知聊天",
			groupMembers: "{{count}}名成员",
			clickToDownload: "点击下载",
			audioMessage: "语音消息",
			videoMessage: "视频消息",
			imageMessage: "图片消息",
			fileMessage: "文件消息",
			systemMessage: "系统消息",
			uploading: "上传中...",
			sendImage: "发送图片",
			sendFile: "发送文件",
			sendAudio: "发送语音",
			sendVideo: "发送视频",
			unsupportedMessageType: "不支持的消息类型",
			replyTo: "回复",
			recallMessage: "撤回消息",
			copyMessage: "复制消息",
			deleteMessage: "删除消息",
			forwardMessage: "转发消息",
			replyMessage: "回复消息",
			editMessage: "编辑消息",
			messageEdited: "消息已编辑",
			messageDeleted: "消息已删除",
			messageForwarded: "消息已转发",
			messageReplied: "消息已回复",
			messageCopied: "消息已复制",
			loadMoreMessages: "加载更多消息",
			noMoreMessages: "没有更多消息了",
			connectionLost: "连接已断开",
			reconnecting: "正在重新连接...",
			reconnected: "连接已恢复",
			typing: "正在输入...",
			online: "在线",
			offline: "离线",
			lastSeen: "最后在线",
			justNow: "刚刚",
			minutesAgo: "分钟前",
			hoursAgo: "小时前",
			daysAgo: "天前",
			weeksAgo: "周前",
			monthsAgo: "月前",
			yearsAgo: "年前",
		},

		// WebSocket连接
		websocket: {
			connecting: "正在连接...",
			connected: "已连接",
			disconnected: "连接已断开",
			reconnecting: "正在重新连接...",
			connectionFailed: "连接失败",
			connectionLost: "连接丢失",
			connectionRestored: "连接已恢复",
			authenticationFailed: "身份验证失败",
			serverUnavailable: "服务器不可用",
			networkError: "网络错误",
			timeout: "连接超时",
			retrying: "正在重试...",
			maxRetriesReached: "已达到最大重试次数",
			willRetryIn: "将在 {{seconds}} 秒后重试",
		},

		// 用户相关
		user: {
			profile: "个人资料",
			avatar: "头像",
			nickname: "昵称",
			nicknamePlaceholder: "请输入昵称",
			account: "账号",
			email: "邮箱",
			emailPlaceholder: "请输入邮箱（可选）",
			phone: "手机号",
			phonePlaceholder: "请输入手机号（可选）",
			status: "状态",
			lastLogin: "最后登录",
			joinDate: "注册时间",
			editProfile: "编辑资料",
			changePassword: "修改密码",
			changeAvatar: "更换头像",
			profileUpdated: "资料已更新",
			passwordChanged: "密码已修改",
			avatarChanged: "头像已更换",
			logout: "退出登录",
			logoutConfirm: "确定要退出登录吗？",
			unknown: "未知用户",
		},

		// 设置
		settings: {
			title: "设置",
			general: "通用设置",
			account: "账号设置",
			security: "安全设置",
			notification: "通知设置",
			appearance: "外观设置",
			language: "语言",
			theme: "主题",
			fontSize: "字体大小",
			autoLogin: "自动登录",
			startupOnBoot: "开机启动",
			soundNotification: "声音通知",
			desktopNotification: "桌面通知",
			messagePreview: "消息预览",
			onlineStatus: "在线状态",
			readReceipt: "已读回执",
			typingIndicator: "输入状态指示",
			dataUsage: "数据使用",
			storage: "存储",
			cache: "缓存",
			clearCache: "清除缓存",
			cacheCleared: "缓存已清除",
			backup: "备份",
			restore: "恢复",
			export: "导出数据",
			import: "导入数据",
			dataExported: "数据已导出",
			dataImported: "数据已导入",
			resetSettings: "重置设置",
			resetConfirm: "确定要重置所有设置吗？",
			settingsReset: "设置已重置",
		},

		// 错误消息
		errors: {
			unknown: "未知错误",
			networkError: "网络错误",
			serverError: "服务器错误",
			authenticationFailed: "身份验证失败",
			permissionDenied: "权限不足",
			resourceNotFound: "资源不存在",
			invalidInput: "输入无效",
			operationFailed: "操作失败",
			timeout: "操作超时",
			connectionFailed: "连接失败",
			fileNotFound: "文件不存在",
			fileTooLarge: "文件过大",
			unsupportedFormat: "不支持的格式",
			quotaExceeded: "配额已超出",
			rateLimitExceeded: "请求过于频繁",
			maintenanceMode: "系统维护中",
			serviceUnavailable: "服务不可用",
			badRequest: "请求错误",
			unauthorized: "未授权",
			forbidden: "禁止访问",
			notFound: "未找到",
			conflict: "冲突",
			internalServerError: "内部服务器错误",
			badGateway: "网关错误",
			serviceTemporarilyUnavailable: "服务暂时不可用",
			gatewayTimeout: "网关超时",
		},

		// 验证消息
		validation: {
			required: "此字段为必填项",
			invalidFormat: "格式不正确",
			tooShort: "长度过短",
			tooLong: "长度过长",
			invalidEmail: "邮箱格式不正确",
			invalidPhone: "手机号格式不正确",
			invalidUrl: "网址格式不正确",
			passwordMismatch: "密码不一致",
			weakPassword: "密码强度过弱",
			invalidCharacters: "包含无效字符",
			duplicateValue: "值重复",
			outOfRange: "超出范围",
			invalidDate: "日期无效",
			futureDate: "不能是未来日期",
			pastDate: "不能是过去日期",
		},
	},
};

export default zhCN;
