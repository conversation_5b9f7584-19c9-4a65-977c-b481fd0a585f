export default function Settings() {
	return (
		<div className="p-4 space-y-6">
			<div className="space-y-2">
				<h1 className="text-2xl font-bold">Settings</h1>
				<p className="text-gray-500">Customize your app experience</p>
			</div>

			<div className="space-y-4">
				<div className="p-4 border rounded-lg">
					<h2 className="font-semibold mb-3">Appearance</h2>
					<div className="flex items-center justify-between">
						<span>Dark Mode</span>
						<label className="relative inline-flex items-center cursor-pointer">
							<input type="checkbox" className="sr-only peer" />
							<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
						</label>
					</div>
				</div>

				<div className="p-4 border rounded-lg">
					<h2 className="font-semibold mb-3">Notifications</h2>
					<div className="space-y-3">
						<div className="flex items-center justify-between">
							<span>Message notifications</span>
							<label className="relative inline-flex items-center cursor-pointer">
								<input type="checkbox" className="sr-only peer" checked />
								<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
							</label>
						</div>
						<div className="flex items-center justify-between">
							<span>Sound notifications</span>
							<label className="relative inline-flex items-center cursor-pointer">
								<input type="checkbox" className="sr-only peer" checked />
								<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
							</label>
						</div>
					</div>
				</div>

				<div className="p-4 border rounded-lg">
					<h2 className="font-semibold mb-3">Privacy</h2>
					<div className="flex items-center justify-between">
						<span>Read receipts</span>
						<label className="relative inline-flex items-center cursor-pointer">
							<input type="checkbox" className="sr-only peer" checked />
							<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
						</label>
					</div>
				</div>
			</div>
		</div>
	);
}
