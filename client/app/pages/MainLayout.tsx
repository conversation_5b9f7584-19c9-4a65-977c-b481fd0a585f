import type React from "react";
import {useState} from "react";
import {useTranslation} from "react-i18next";
import ChatList from "../components/ChatList";
import ContactList from "../components/ContactList";
import {Button} from "../components/ui/button";
import {Card, CardContent} from "../components/ui/card";
import Chat from "./Chat";
import MainNav from "@/components/MainNav";
import {logout} from "@/services/auth";
import {useAtomValue} from "jotai";
import {userAtom} from "@/jotai/atoms/user";

type TabType = "chats" | "contacts";

const MainLayout: React.FC = () => {
	const {t} = useTranslation();
	const [activeTab, setActiveTab] = useState<TabType>("chats");
	const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
	const user = useAtomValue(userAtom);

	if (!user) {
		return null;
	}

	return (
		<div className="h-screen flex bg-gray-50 overflow-hidden">
			{/* 左侧导航栏 */}
			<MainNav />
			<div className="w-80 border-r bg-white flex flex-col">
				{/* 顶部用户信息 */}
				<Card className="rounded-none border-b">
					<CardContent className="p-4">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
									<span className="text-primary-foreground font-medium">
										{user.nickname.charAt(0)}
									</span>
								</div>
								<div>
									<h3 className="font-medium text-sm">{user.nickname}</h3>
									<p className="text-xs text-muted-foreground">
										{user.account}
									</p>
								</div>
							</div>
							<Button
								variant="ghost"
								size="sm"
								onClick={logout}
								className="text-red-600 hover:text-red-700 hover:bg-red-50"
							>
								{t("user.logout")}
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* 标签页切换 */}
				<div className="flex border-b">
					<button
						type="button"
						onClick={() => setActiveTab("chats")}
						className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
							activeTab === "chats"
								? "text-primary border-b-2 border-primary bg-primary/5"
								: "text-muted-foreground hover:text-foreground"
						}`}
					>
						{t("nav.chats")}
					</button>
					<button
						type="button"
						onClick={() => setActiveTab("contacts")}
						className={`flex-1 py-3 px-4 text-sm font-medium transition-colors ${
							activeTab === "contacts"
								? "text-primary border-b-2 border-primary bg-primary/5"
								: "text-muted-foreground hover:text-foreground"
						}`}
					>
						{t("nav.contacts")}
					</button>
				</div>

				{/* 内容区域 */}
				<div className="flex-1 overflow-hidden">
					{activeTab === "chats" && (
						<ChatList
							selectedChatId={selectedChatId}
							onChatSelect={setSelectedChatId}
						/>
					)}
					{activeTab === "contacts" && <ContactList />}
				</div>
			</div>

			{/* 右侧聊天区域 */}
			<div className="flex-1 flex flex-col">
				{selectedChatId ? (
					<Chat chatId={selectedChatId} />
				) : (
					<div className="flex-1 flex items-center justify-center bg-gray-50">
						<div className="text-center text-muted-foreground">
							<svg
								className="w-16 h-16 mx-auto mb-4 text-muted-foreground/50"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								role="img"
							>
								<title>Select Chat Icon</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
								/>
							</svg>
							<h3 className="text-lg font-medium mb-2">
								{t("chat.selectChatToStart")}
							</h3>
							<p className="text-sm">{t("chat.selectChatDescription")}</p>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default MainLayout;
