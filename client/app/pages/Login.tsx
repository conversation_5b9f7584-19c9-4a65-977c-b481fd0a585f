import type React from "react";
import {useEffect, useId, useState} from "react";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router";
import {Button} from "../components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../components/ui/card";
import {Input} from "../components/ui/input";
import {Label} from "../components/ui/label";
import {getIdentifier, loginSafe} from "@/services/auth";
import {
	decryptPassword,
	deleteUser,
	encryptPassword,
	getDefaultUser,
	getUsers,
	saveSettings,
	saveUser,
} from "@/services/storage";

const Login: React.FC = () => {
	const {t} = useTranslation();
	const navigate = useNavigate();
	const serverUrlId = useId();
	const accountId = useId();
	const passwordId = useId();
	const userSelectId = useId();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [savedUsers, setSavedUsers] = useState<UserCredentials[]>([]);
	const [selectedUserId, setSelectedUserId] = useState<string>("");

	// 登录表单状态
	const [loginForm, setLoginForm] = useState({
		url: "http://localhost:8080",
		account: "",
		password: "",
		rememberPassword: true,
		autoLogin: false,
		startupOnBoot: false,
	});

	// 加载保存的用户
	useEffect(() => {
		const users = getUsers();
		setSavedUsers(users);

		const defaultUser = getDefaultUser();
		if (defaultUser) {
			setSelectedUserId(defaultUser.id);
			setLoginForm((prev) => ({
				...prev,
				url: defaultUser.url,
				account: defaultUser.account,
			}));
		}
	}, []);

	// 处理用户选择
	const handleUserSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
		const userId = e.target.value;
		setSelectedUserId(userId);

		if (userId) {
			const user = savedUsers.find((u) => u.id === userId);
			if (user) {
				setLoginForm((prev) => ({
					...prev,
					url: user.url,
					account: user.account,
				}));
			}
		} else {
			setLoginForm((prev) => ({
				...prev,
				url: "http://localhost:8080",
				account: "",
				password: "",
			}));
		}
		setError("");
	};

	// 快速登录
	const handleQuickLogin = async () => {
		if (!selectedUserId) return;

		const user = savedUsers.find((u) => u.id === selectedUserId);
		if (!user) return;

		const password = decryptPassword(user.encryptedPassword);
		if (!password) {
			setError(t("login.passwordNotSaved"));
			return;
		}

		setIsLoading(true);
		setError("");

		try {
			const result = await loginSafe({
				serverUrl: user.url,
				account: user.account,
				password: password,
			});

			if (result.success) {
				navigate("/chat");
			} else {
				setError(result.error);
			}
		} catch (error) {
			console.error("快速登录失败:", error);
			setError(t("login.networkError"));
		} finally {
			setIsLoading(false);
		}
	};

	// 删除用户
	const handleDeleteUser = () => {
		if (!selectedUserId) return;

		deleteUser(selectedUserId);
		const users = getUsers();
		setSavedUsers(users);
		setSelectedUserId("");
		setLoginForm((prev) => ({
			...prev,
			url: "http://localhost:8080",
			account: "",
			password: "",
		}));
	};

	// 处理登录表单变化
	const handleLoginFormChange = (
		field: KeyOf<typeof loginForm>,
		value: ValueOf<typeof loginForm>,
	) => {
		setLoginForm((prev) => ({...prev, [field]: value}));
		if (error) setError("");
	};

	// 处理登录
	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);
		setError("");

		const result = await loginSafe({
			serverUrl: loginForm.url,
			account: loginForm.account,
			password: loginForm.password,
		});

		if (!result.success) {
			setError(result.error);
			setIsLoading(false);
			return;
		}

		// 如果选择记住密码，保存用户信息
		if (loginForm.rememberPassword) {
			const {url: serverUrl, account, password} = loginForm;
			saveUser({
				id: getIdentifier(serverUrl, account),
				url: serverUrl,
				account: account,
				encryptedPassword: encryptPassword(password),
				nickname: result.user.nickname,
				avatar: result.user.avatar,
				lastLoginTime: Date.now(),
			});
		}
		saveSettings({
			autoLogin: loginForm.autoLogin,
			startupOnBoot: loginForm.startupOnBoot,
		});
		setIsLoading(false);

		navigate("/chat");
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
			<div className="w-full max-w-md">
				{/* Logo 和标题 */}
				<div className="text-center mb-8">
					<div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg
							className="w-8 h-8 text-white"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
							aria-label={t("app.logo")}
						>
							<title>{t("app.logo")}</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
							/>
						</svg>
					</div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						{t("app.name")}
					</h1>
					<p className="text-gray-600">{t("login.subtitle")}</p>
				</div>

				{/* 登录表单 */}
				<Card>
					<CardHeader>
						<CardTitle>{t("login.title")}</CardTitle>
						<CardDescription>{t("login.description")}</CardDescription>
					</CardHeader>
					<CardContent>
						{/* 错误信息 */}
						{error && (
							<div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
								<p className="text-sm text-red-600">{error}</p>
							</div>
						)}

						{/* 保存的用户列表 */}
						{savedUsers.length > 0 && (
							<div className="mb-6 space-y-2">
								<Label htmlFor={userSelectId}>{t("login.selectUser")}</Label>
								<select
									id={userSelectId}
									value={selectedUserId}
									onChange={handleUserSelect}
									className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								>
									<option value="">{t("login.newUser")}</option>
									{savedUsers.map((user) => (
										<option key={user.id} value={user.id}>
											{user.nickname} ({user.account})
										</option>
									))}
								</select>
								{selectedUserId && (
									<div className="flex space-x-2">
										<Button
											type="button"
											onClick={handleQuickLogin}
											disabled={isLoading}
											className="flex-1"
											variant="default"
										>
											{isLoading ? t("common.loading") : t("login.quickLogin")}
										</Button>
										<Button
											type="button"
											onClick={handleDeleteUser}
											variant="destructive"
										>
											{t("login.deleteUser")}
										</Button>
									</div>
								)}
							</div>
						)}

						{/* 分隔线 */}
						{savedUsers.length > 0 && (
							<div className="relative mb-6">
								<div className="absolute inset-0 flex items-center">
									<div className="w-full border-t border-gray-300" />
								</div>
								<div className="relative flex justify-center text-sm">
									<span className="px-2 bg-white text-gray-500">
										{t("login.orLoginWith")}
									</span>
								</div>
							</div>
						)}

						{/* 登录表单 */}
						<form onSubmit={handleLogin} className="space-y-4">
							{/* 服务器地址 */}
							<div className="space-y-2">
								<Label htmlFor={serverUrlId}>{t("common.serverUrl")}</Label>
								<Input
									id={serverUrlId}
									type="url"
									value={loginForm.url}
									onChange={(e) => handleLoginFormChange("url", e.target.value)}
									placeholder={t("common.serverUrlPlaceholder")}
									required
								/>
							</div>

							{/* 账号 */}
							<div className="space-y-2">
								<Label htmlFor={accountId}>{t("common.account")}</Label>
								<Input
									id={accountId}
									type="text"
									value={loginForm.account}
									onChange={(e) =>
										handleLoginFormChange("account", e.target.value)
									}
									placeholder={t("common.accountPlaceholder")}
									required
								/>
							</div>

							{/* 密码 */}
							<div className="space-y-2">
								<Label htmlFor={passwordId}>{t("common.password")}</Label>
								<Input
									id={passwordId}
									type="password"
									value={loginForm.password}
									onChange={(e) =>
										handleLoginFormChange("password", e.target.value)
									}
									placeholder={t("common.passwordPlaceholder")}
									required
								/>
							</div>

							{/* 选项 */}
							<div className="space-y-2">
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={loginForm.rememberPassword}
										onChange={(e) =>
											handleLoginFormChange(
												"rememberPassword",
												e.target.checked,
											)
										}
										className="mr-2"
									/>
									<span className="text-sm text-gray-700">
										{t("login.rememberPassword")}
									</span>
								</label>
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={loginForm.autoLogin}
										onChange={(e) =>
											handleLoginFormChange("autoLogin", e.target.checked)
										}
										className="mr-2"
									/>
									<span className="text-sm text-gray-700">
										{t("login.autoLogin")}
									</span>
								</label>
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={loginForm.startupOnBoot}
										onChange={(e) =>
											handleLoginFormChange("startupOnBoot", e.target.checked)
										}
										className="mr-2"
									/>
									<span className="text-sm text-gray-700">
										{t("login.startupOnBoot")}
									</span>
								</label>
							</div>

							{/* 登录按钮 */}
							<Button type="submit" disabled={isLoading} className="w-full">
								{isLoading ? t("common.loading") : t("login.submit")}
							</Button>
						</form>

						{/* 切换到注册 */}
						<div className="mt-6 text-center">
							<p className="text-sm text-muted-foreground">
								{t("login.noAccount")}{" "}
								<Button
									variant="link"
									onClick={() => navigate("/register")}
									className="p-0 h-auto font-medium"
								>
									{t("register.title")}
								</Button>
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default Login;
