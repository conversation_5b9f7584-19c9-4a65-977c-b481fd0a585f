import type React from "react";
import {useCallback, useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import MessageBubble from "../components/MessageBubble";
import MessageInput from "../components/MessageInput";
import {Card, CardHeader, CardTitle} from "../components/ui/card";
import type {Chat as ChatType, Message} from "../services/database";
// import { dbManager } from "../services/database";
import {ws} from "../services/websocket";
import {getUser, logout} from "@/services/auth";
import {Button} from "@/components/ui/button";
import {useAtomValue} from "jotai";
import {dbAtom} from "@/jotai/atoms/db";

interface ChatProps {
	chatId: string;
}

const Chat: React.FC<ChatProps> = ({chatId}) => {
	const {t} = useTranslation();
	const [messages, setMessages] = useState<Message[]>([]);
	const [isConnected, setIsConnected] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [chatInfo, setChatInfo] = useState<ChatType | null>(null);
	const [_unreadCount, _setUnreadCount] = useState(0);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const db = useAtomValue(dbAtom);

	// 自动滚动到底部
	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({behavior: "smooth"});
	});

	// 添加系统消息
	const addSystemMessage = useCallback(
		(content: string) => {
			const message: Message = {
				messageId: Date.now().toString(),
				chatId: chatId,
				content,
				timestamp: Date.now(),
				sender: "System",
				senderNickname: "系统",
				type: "system",
				status: "sent",
			};
			setMessages((prev) => [...prev, message]);
		},
		[chatId],
	);

	// 加载聊天信息和历史消息
	const loadChatData = useCallback(async () => {
		setIsLoading(true);
		try {
			if (!db) {
				console.error("数据库未初始化");
				return;
			}

			// 加载聊天信息
			const chat = await db.chats.where("chatId").equals(chatId).first();
			setChatInfo(chat || null);

			// 加载历史消息
			const historyMessages = await db.getMessages(chatId, 50, 0);
			setMessages(historyMessages);
		} catch (error) {
			console.error("加载聊天数据失败:", error);
		} finally {
			setIsLoading(false);
		}
	}, [chatId]);

	// 初始化聊天数据
	useEffect(() => {
		loadChatData();
	}, [loadChatData]);

	// 初始化WebSocket连接
	useEffect(() => {
		const initializeWebSocket = () => {
			const wsUrl = getWebSocketUrl();
			if (wsUrl) {
				console.log("正在连接WebSocket:", wsUrl);
				ws.connect(wsUrl);

				// 监听连接状态变化
				const unsubscribeConnection = ws.onStateChange((connected) => {
					setIsConnected(connected);
					if (connected) {
						addSystemMessage(t("websocket.connected"));
					} else {
						addSystemMessage(t("websocket.disconnected"));
					}
				});

				// 监听消息
				const unsubscribeMessage = ws.onMessage(
					"chat_message",
					(data: unknown) => {
						const messageData = data as {
							id?: string;
							content?: string;
							message?: string;
							sender?: string;
							timestamp?: number;
						};
						const message: Message = {
							id: messageData.id || Date.now(),
							content:
								messageData.content ||
								messageData.message ||
								JSON.stringify(data),
							timestamp: messageData.timestamp || Date.now(),
							sender: messageData.sender || "Unknown",
							type: "text",
						};
						setMessages((prev) => [...prev, message]);
					},
				);

				return () => {
					unsubscribeConnection();
					unsubscribeMessage();
					ws.disconnect();
				};
			} else {
				console.error("无法获取WebSocket URL");
				addSystemMessage(t("websocket.connectionFailed"));
			}
		};

		const cleanup = initializeWebSocket();
		return cleanup;
	}, [t, addSystemMessage]);

	// 发送消息
	const handleSendMessage = async (
		content: string,
		type: "text" | "image" | "file" | "audio" | "video",
	) => {
		if (!content.trim() || !isConnected) {
			return;
		}

		setIsLoading(true);

		try {
			const currentUser = getUser();
			if (!currentUser) {
				addSystemMessage(t("chat.userNotFound"));
				return;
			}

			const messageId = Date.now().toString();
			const message: Message = {
				messageId,
				chatId,
				content: content.trim(),
				timestamp: Date.now(),
				sender: currentUser.account,
				senderNickname: currentUser.nickname,
				type,
				status: "sending",
			};

			// 先添加到界面显示（状态为发送中）
			setMessages((prev) => [...prev, message]);

			const success = ws.send({
				type: "chat_message",
				data: {
					messageId,
					chatId,
					content: message.content,
					type,
				},
			});

			if (success) {
				// 保存到本地数据库
				if (db) {
					await db.addMessage({
						messageId,
						chatId,
						content: message.content,
						timestamp: message.timestamp,
						sender: message.sender,
						senderNickname: message.senderNickname,
						type,
						status: "sent",
					});

					// 更新消息状态为已发送
					setMessages((prev) =>
						prev.map((msg) =>
							msg.messageId === messageId ? {...msg, status: "sent"} : msg,
						),
					);
				}
			} else {
				// 发送失败，更新状态
				setMessages((prev) =>
					prev.map((msg) =>
						msg.messageId === messageId ? {...msg, status: "failed"} : msg,
					),
				);
				addSystemMessage(t("chat.messageFailed"));
			}
		} catch (error) {
			console.error("发送消息失败:", error);
			addSystemMessage(t("chat.messageFailed"));
		} finally {
			setIsLoading(false);
		}
	};

	// 登出处理
	const _handleLogout = () => {
		if (confirm(t("user.logoutConfirm"))) {
			ws.disconnect();
			logout();
		}
	};

	// 格式化时间
	const _formatTime = (timestamp: number) => {
		return new Date(timestamp).toLocaleTimeString("zh-CN", {
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	return (
		<div className="h-screen flex flex-col bg-gray-50">
			{/* 顶部栏 */}
			<Card className="rounded-none border-b">
				<CardHeader className="py-3">
					<div className="flex items-center justify-between">
						<div className="flex items-center space-x-3">
							{/* 聊天头像 */}
							<div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
								{chatInfo?.avatar ? (
									<img
										src={chatInfo.avatar}
										alt={chatInfo.name}
										className="w-10 h-10 rounded-full object-cover"
									/>
								) : (
									<span className="text-gray-600 font-medium">
										{chatInfo?.name?.charAt(0)?.toUpperCase() || "?"}
									</span>
								)}
							</div>

							{/* 聊天信息 */}
							<div>
								<CardTitle className="text-lg">
									{chatInfo?.name || t("chat.unknownChat")}
								</CardTitle>
								<div className="flex items-center space-x-2">
									<div
										className={`w-2 h-2 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
									></div>
									<span className="text-sm text-muted-foreground">
										{chatInfo?.type === "group"
											? t("chat.groupMembers", {
													count: chatInfo.memberCount || 0,
												})
											: isConnected
												? t("websocket.connected")
												: t("websocket.disconnected")}
									</span>
								</div>
							</div>
						</div>

						<div className="flex items-center space-x-2">
							{/* 聊天操作按钮 */}
							<Button variant="ghost" size="sm">
								<svg
									className="w-4 h-4"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
									role="img"
								>
									<title>Chat Options</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
									/>
								</svg>
							</Button>
						</div>
					</div>
				</CardHeader>
			</Card>

			{/* 消息区域 */}
			<div className="flex-1 overflow-y-auto p-4 space-y-4">
				{messages.length === 0 ? (
					<div className="flex items-center justify-center h-full text-gray-500">
						<div className="text-center">
							<svg
								className="w-16 h-16 mx-auto mb-4 text-gray-300"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>Chat Icon</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
								/>
							</svg>
							<p className="text-lg font-medium">{t("chat.noChatsYet")}</p>
							<p className="text-sm">{t("chat.startNewChat")}</p>
						</div>
					</div>
				) : (
					messages.map((message, index) => {
						const prevMessage = index > 0 ? messages[index - 1] : null;
						const nextMessage =
							index < messages.length - 1 ? messages[index + 1] : null;

						// 判断是否显示头像和时间
						const showAvatar =
							!prevMessage || prevMessage.sender !== message.sender;
						const showTime =
							!nextMessage ||
							nextMessage.sender !== message.sender ||
							nextMessage.timestamp - message.timestamp > 5 * 60 * 1000; // 5分钟间隔

						return (
							<MessageBubble
								key={message.messageId}
								message={message}
								showAvatar={showAvatar}
								showTime={showTime}
							/>
						);
					})
				)}
				<div ref={messagesEndRef} />
			</div>

			{/* 输入区域 */}
			<MessageInput
				onSendMessage={handleSendMessage}
				disabled={!isConnected || isLoading}
			/>
		</div>
	);
};

export default Chat;
