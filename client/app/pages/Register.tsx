import type React from "react";
import {useId, useState} from "react";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router";
import {Button} from "../components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "../components/ui/card";
import {Input} from "../components/ui/input";
import {Label} from "../components/ui/label";
import {registerSafe, type RegisterRequest} from "../services/auth";

const Register: React.FC = () => {
	const {t} = useTranslation();
	const navigate = useNavigate();
	const serverUrlId = useId();
	const accountId = useId();
	const passwordId = useId();
	const confirmPasswordId = useId();
	const nicknameId = useId();
	const emailId = useId();
	const phoneId = useId();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");

	// 注册表单状态
	const [registerForm, setRegisterForm] = useState({
		serverUrl: "http://localhost:8080",
		account: "",
		password: "",
		confirmPassword: "",
		nickname: "",
		email: "",
		phone: "",
	});

	// 处理注册表单变化
	const handleRegisterFormChange = (field: string, value: string) => {
		setRegisterForm((prev) => ({...prev, [field]: value}));
		if (error) setError("");
	};

	// 验证注册表单
	const validateRegisterForm = (): boolean => {
		if (!registerForm.serverUrl.trim()) {
			setError(t("validation.serverUrlRequired"));
			return false;
		}
		if (!registerForm.account.trim()) {
			setError(t("validation.accountRequired"));
			return false;
		}
		if (!registerForm.password.trim()) {
			setError(t("validation.passwordRequired"));
			return false;
		}
		if (registerForm.password !== registerForm.confirmPassword) {
			setError(t("validation.passwordMismatch"));
			return false;
		}
		if (registerForm.password.length < 6) {
			setError(t("validation.passwordTooShort"));
			return false;
		}
		return true;
	};

	// 处理注册
	const handleRegister = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!validateRegisterForm()) return;

		setIsLoading(true);
		setError("");

		try {
			const request: RegisterRequest = {
				serverUrl: registerForm.serverUrl,
				account: registerForm.account,
				password: registerForm.password,
				nickname: registerForm.nickname || registerForm.account,
				email: registerForm.email || undefined,
				phone: registerForm.phone || undefined,
			};

			const result = await registerSafe(request);

			if (result.success) {
				navigate("/login");
				return;
			}

			setError(result.message);
		} catch (error) {
			console.error("注册失败:", error);
			setError(t("register.networkError"));
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
			<div className="w-full max-w-md">
				{/* Logo 和标题 */}
				<div className="text-center mb-8">
					<div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg
							className="w-8 h-8 text-white"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							role="img"
							aria-label={t("app.logo")}
						>
							<title>{t("app.logo")}</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
							/>
						</svg>
					</div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						{t("app.name")}
					</h1>
					<p className="text-gray-600">{t("register.subtitle")}</p>
				</div>

				{/* 注册表单 */}
				<Card>
					<CardHeader>
						<CardTitle>{t("register.title")}</CardTitle>
						<CardDescription>{t("register.description")}</CardDescription>
					</CardHeader>
					<CardContent>
						{error && (
							<div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
								<p className="text-sm text-red-600">{error}</p>
							</div>
						)}

						<form onSubmit={handleRegister} className="space-y-4">
							{/* 服务器地址 */}
							<div className="space-y-2">
								<Label htmlFor={serverUrlId}>{t("common.serverUrl")}</Label>
								<Input
									id={serverUrlId}
									type="url"
									value={registerForm.serverUrl}
									onChange={(e) =>
										handleRegisterFormChange("serverUrl", e.target.value)
									}
									placeholder={t("common.serverUrlPlaceholder")}
									required
								/>
							</div>

							{/* 账号 */}
							<div className="space-y-2">
								<Label htmlFor={accountId}>{t("common.account")}</Label>
								<Input
									id={accountId}
									type="text"
									value={registerForm.account}
									onChange={(e) =>
										handleRegisterFormChange("account", e.target.value)
									}
									placeholder={t("common.accountPlaceholder")}
									required
								/>
							</div>

							{/* 密码 */}
							<div className="space-y-2">
								<Label htmlFor={passwordId}>{t("common.password")}</Label>
								<Input
									id={passwordId}
									type="password"
									value={registerForm.password}
									onChange={(e) =>
										handleRegisterFormChange("password", e.target.value)
									}
									placeholder={t("common.passwordPlaceholder")}
									required
								/>
							</div>

							{/* 确认密码 */}
							<div className="space-y-2">
								<Label htmlFor={confirmPasswordId}>
									{t("register.confirmPassword")}
								</Label>
								<Input
									id={confirmPasswordId}
									type="password"
									value={registerForm.confirmPassword}
									onChange={(e) =>
										handleRegisterFormChange("confirmPassword", e.target.value)
									}
									placeholder={t("register.confirmPasswordPlaceholder")}
									required
								/>
							</div>

							{/* 昵称 */}
							<div className="space-y-2">
								<Label htmlFor={nicknameId}>{t("user.nickname")}</Label>
								<Input
									id={nicknameId}
									type="text"
									value={registerForm.nickname}
									onChange={(e) =>
										handleRegisterFormChange("nickname", e.target.value)
									}
									placeholder={t("user.nicknamePlaceholder")}
								/>
							</div>

							{/* 邮箱 */}
							<div className="space-y-2">
								<Label htmlFor={emailId}>{t("user.email")}</Label>
								<Input
									id={emailId}
									type="email"
									value={registerForm.email}
									onChange={(e) =>
										handleRegisterFormChange("email", e.target.value)
									}
									placeholder={t("user.emailPlaceholder")}
								/>
							</div>

							{/* 手机号 */}
							<div className="space-y-2">
								<Label htmlFor={phoneId}>{t("user.phone")}</Label>
								<Input
									id={phoneId}
									type="tel"
									value={registerForm.phone}
									onChange={(e) =>
										handleRegisterFormChange("phone", e.target.value)
									}
									placeholder={t("user.phonePlaceholder")}
								/>
							</div>

							{/* 注册按钮 */}
							<Button type="submit" disabled={isLoading} className="w-full">
								{isLoading ? t("common.loading") : t("register.submit")}
							</Button>
						</form>

						{/* 切换到登录 */}
						<div className="mt-6 text-center">
							<p className="text-sm text-muted-foreground">
								{t("register.hasAccount")}{" "}
								<Button
									variant="link"
									onClick={() => navigate("/login")}
									className="p-0 h-auto font-medium"
								>
									{t("login.title")}
								</Button>
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default Register;
