import CryptoJS from "crypto-js";

// 应用设置接口
export type AppSettings = {
	autoLogin: boolean;
	rememberPassword: boolean;
	startupOnBoot: boolean;
	language: string;
	theme: string;
	fontSize: number;
	soundNotification: boolean;
	desktopNotification: boolean;
};

const defaultSettings: AppSettings = Object.freeze({
	autoLogin: false,
	rememberPassword: true,
	startupOnBoot: false,
	language: "zh-CN",
	theme: "light",
	fontSize: 14,
	soundNotification: true,
	desktopNotification: true,
});

const STORAGE_KEYS = Object.freeze({
	USERS: "liyu_users",
	SETTINGS: "liyu_settings",
});

// 生成基于机器特征的加密密钥
const generateEncryptionKey = () => {
	const machineInfo = [
		navigator.userAgent,
		navigator.language,
		screen.width,
		screen.height,
		new Date().getTimezoneOffset(),
	].join("|");

	return CryptoJS.SHA256(`${machineInfo}liyu_secret_key_2024`)
		.toString()
		.substring(0, 32);
};

const ENCRYPTION_KEY = generateEncryptionKey();

// 加密保存的密码
export const encryptPassword = (password: string) =>
	CryptoJS.AES.encrypt(password, ENCRYPTION_KEY).toString();

// 解密密码
export const decryptPassword = (encryptedPassword: string) => {
	try {
		const bytes = CryptoJS.AES.decrypt(encryptedPassword, ENCRYPTION_KEY);
		return bytes.toString(CryptoJS.enc.Utf8);
	} catch (error) {
		console.error("密码解密失败:", error);
		return "";
	}
};

// 获取所有用户
export const getUsers = (): UserCredentials[] => {
	try {
		const usersJson = localStorage.getItem(STORAGE_KEYS.USERS);
		return usersJson ? JSON.parse(usersJson) : [];
	} catch (error) {
		console.error("获取用户列表失败:", error);
		return [];
	}
};

// 保存用户信息（包含密码）
export const saveUser = (user: UserCredentials) => {
	const users = getUsers();
	const existingIndex = users.findIndex((u) => u.id === user.id);

	if (existingIndex >= 0) {
		users[existingIndex] = user;
	} else {
		users.push(user);
	}

	localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
};

// 删除用户
export const deleteUser = (userId: string) => {
	const users = getUsers().filter((u) => u.id !== userId);
	localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));
};

// 获取默认用户
export const getDefaultUser = (): UserCredentials | null => {
	const users = getUsers();
	let user: UserCredentials | null = null;

	for (const u of users) {
		if (!user || u.lastLoginTime > user.lastLoginTime) {
			user = u;
		}
	}

	return user;
};

// 获取应用设置
export const getSettings = (): AppSettings => {
	try {
		const settingsJson = localStorage.getItem(STORAGE_KEYS.SETTINGS);

		return settingsJson
			? {...defaultSettings, ...JSON.parse(settingsJson)}
			: defaultSettings;
	} catch (error) {
		console.error("获取应用设置失败:", error);
		return defaultSettings;
	}
};

// 保存应用设置
export const saveSettings = (settings: Partial<AppSettings>) => {
	const currentSettings = getSettings();
	const newSettings = {...currentSettings, ...settings};
	localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(newSettings));
};
