import CryptoJS from "crypto-js";
import {closeDB, deleteDB, openDB} from "./database";
import {
	decryptPassword,
	deleteUser,
	getDefaultUser,
	getSettings,
} from "./storage";
import {ws} from "./websocket";
import {defaultStore} from "@/jotai/stores/default";
import {userAtom} from "@/jotai/atoms/user";
import {loginRequest, registerRequest, validateRequest} from "./request";
import {Member} from "@/core/member";

// 登录请求接口
export type LoginRequest = {
	account: string;
	password: string;
	serverUrl: string;
};

// 注册请求接口
export type RegisterRequest = {
	serverUrl: string;
	account: string;
	password: string;
	nickname: string;
	email?: string;
	phone?: string;
};

type LoginSuccessResponse = {
	success: true;
	user: Member;
};

type LoginFailureResponse = {
	success: false;
	error: string;
};

// 登录响应接口
export type LoginResponse = LoginSuccessResponse | LoginFailureResponse;

// 生成用户唯一标识符
export const getIdentifier = (account: string, serverUrl: string) => {
	// 解析服务器URL获取域名和端口
	try {
		const url = new URL(serverUrl);
		const host = url.hostname;
		const port = url.port || (url.protocol === "https:" ? "443" : "80");
		return `${account}@${host}:${port}`;
	} catch {
		console.error("Invalid server URL:", serverUrl);
		// 如果URL解析失败，使用简化格式
		return `${account}@${serverUrl.replace(/^https?:\/\//, "")}`;
	}
};

export const getUser = () => defaultStore.get(userAtom);

// 密码哈希（用于网络传输）
export const hashPassword = (account: string, password: string) =>
	CryptoJS.SHA256(account + password).toString();

// 构建API URL
export const buildApiUrl = (serverUrl: string, endpoint: string) => {
	const baseUrl = serverUrl.endsWith("/") ? serverUrl.slice(0, -1) : serverUrl;
	return `${baseUrl}/api/v1${endpoint}`;
};

// 登录
export const loginSafe = async (request: LoginRequest): Promise<LoginResponse> => {
	try {
		const {serverUrl, account, password} = request;
		const hashedPassword = hashPassword(account, password);
		const response = await loginRequest(serverUrl, account, hashedPassword);
		if (!response.success || !response.data) {
			return {
				success: false,
				error: response.error?.message || response.message,
			};
		}

		const {user, websocketUrl} = response.data;
		await ws.connect(websocketUrl);
		await openDB(account, serverUrl);
		defaultStore.set(userAtom, user);
		return {
			success: true,
			user: user,
		};
	} catch (error) {
		console.error("登录请求失败:", error);
		logout();
		return {
			success: false,
			error: String(error),
		};
	}
};

// 自动登录
export const autoLoginSafe = async () => {
	const settings = getSettings();
	if (!settings.autoLogin) {
		return false;
	}

	const defaultUser = getDefaultUser();
	if (!defaultUser) {
		return false;
	}

	const password = decryptPassword(defaultUser.encryptedPassword);
	if (!password) {
		return false;
	}

	const response = await loginSafe({
		account: defaultUser.account,
		password: password,
		serverUrl: defaultUser.url,
	});

	return response.success;
};

// 登出
export const logout = () => {
	// 断开WebSocket连接
	ws.disconnect();

	// 关闭数据库（不删除数据）
	closeDB();

	// 清除存储的当前用户
	defaultStore.set(userAtom, null);
};

// 验证token是否有效
export const validateTokenSafe = async (url: string, token: string) => {
	try {
		const response = await validateRequest(url, token);
		if (response.success && response.data) {
			defaultStore.set(userAtom, response.data);
			return true;
		}

		// Token无效，清除登录状态
		logout();
		return false;
	} catch (error) {
		console.error("Token验证失败:", error);
		return false;
	}
};

// 删除用户数据（包括数据库）
export const deleteUserData = async (userCredentials: UserCredentials) => {
	try {
		// 删除用户数据库
		await deleteDB(userCredentials.account, userCredentials.url);

		// 从存储中删除用户凭据
		deleteUser(userCredentials.id);

		console.log(
			`用户数据已删除: ${userCredentials.account}@${userCredentials.url}`,
		);
	} catch (error) {
		console.error("删除用户数据失败:", error);
		throw error;
	}
};

// 注册
export const registerSafe = async (request: RegisterRequest) => {
	try {
		// 密码哈希处理
		const hashedPassword = hashPassword(request.account, request.password);

		const response = await registerRequest({
			...request,
			password: hashedPassword,
		});
		return {
			success: response.success,
			message: response.message,
		};
	} catch (error) {
		console.error("注册请求失败:", error);
		return {
			success: false,
			message: String(error),
		};
	}
};
