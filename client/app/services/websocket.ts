import {MINUTE, SECOND} from "@/constants/time";
import {promiseWithTimeout} from "@/lib/promise";
import {Subject} from "rxjs";

// WebSocket消息接口
export type WebSocketMessage<T = unknown> = {
	type: string;
	data: T;
};

const CLOSE_REASON = Object.freeze({
	normal: "normal",
	heartbeat_error: "heartbeat_error",
});

// WebSocket管理器
class Ws {
	private ws: WebSocket | null = null;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectDelay = 1000;
	private heartbeatInterval: NodeJS.Timeout | null = null;
	private heartbeatTimeout: NodeJS.Timeout | null = null;
	private messageHandlers: Map<string, Set<(data: unknown) => void>> =
		new Map();
	private subject = new Subject<number>();

	public get url() {
		return this.ws?.url ?? "";
	}

	// 获取连接状态
	public get state() {
		return this.ws?.readyState ?? WebSocket.CLOSED;
	}

	// 检查是否已连接
	public get isConnected() {
		return this.ws?.readyState === WebSocket.OPEN;
	}

	// 连接WebSocket
	public connect(url: string) {
		const readyWs = () =>
			promiseWithTimeout(
				new Promise<this>((resolve) => {
					const unsubscribe = ws.onStateChange((state) => {
						if (state === WebSocket.OPEN) {
							unsubscribe();
							resolve(this);
						}
					});
					ws.createConnection(url);
				}),
				10 * 1000,
				"WebSocket连接超时",
			);

		if (!this.ws) {
			return readyWs();
		}

		if (this.ws.readyState === WebSocket.OPEN && this.ws.url === url) {
			return Promise.resolve(this);
		}

		this.disconnect();
		return readyWs();
	}

	// 创建WebSocket连接
	private createConnection(url: string) {
		this.ws = new WebSocket(url);

		this.ws.onopen = () => {
			console.log("WebSocket连接已建立");
			this.reconnectAttempts = 0;
			this.emitStateChange(WebSocket.OPEN);
			this.startHeartbeat();
		};

		this.ws.onmessage = (event) => {
			try {
				const message = JSON.parse(event.data);
				this.handleMessage(message);
			} catch (error) {
				console.error("解析WebSocket消息失败:", error);
			}
		};

		this.ws.onclose = (event) => {
			console.log("WebSocket连接已关闭:", event.code, event.reason);
			this.emitStateChange(WebSocket.CLOSED);
			this.stopHeartbeat();

			// 如果不是正常关闭，尝试重连
			if (
				event.reason !== CLOSE_REASON.normal &&
				this.reconnectAttempts < this.maxReconnectAttempts &&
				this.ws
			) {
				this.scheduleReconnect(this.ws.url);
			}
		};

		this.ws.onerror = (error) => {
			console.error("WebSocket连接错误:", error);
			this.emitStateChange(WebSocket.CLOSED);
		};
	}

	// 安排重连
	private scheduleReconnect(url: string) {
		this.reconnectAttempts++;
		const delay = this.reconnectDelay * 2 ** (this.reconnectAttempts - 1);

		console.log(`将在 ${delay}ms 后尝试第 ${this.reconnectAttempts} 次重连`);

		setTimeout(() => {
			if (this.reconnectAttempts <= this.maxReconnectAttempts) {
				console.log(`正在进行第 ${this.reconnectAttempts} 次重连尝试`);
				this.createConnection(url);
			}
		}, delay);
	}

	// 处理收到的消息
	private handleMessage(message: WebSocketMessage) {
		// 处理心跳响应
		if (message.type === "pong") {
			this.handlePong();
			return;
		}

		const handlers = this.messageHandlers.get(message.type);
		if (handlers) {
			handlers.forEach((handler) => {
				try {
					handler(message.data);
				} catch (error) {
					console.error("处理WebSocket消息失败:", error);
				}
			});
		}
	}

	// 注册消息处理器
	public onMessage(type: string, handler: (data: unknown) => void) {
		if (!this.messageHandlers.has(type)) {
			this.messageHandlers.set(type, new Set());
		}
		this.messageHandlers.get(type)!.add(handler);

		// 返回取消注册的函数
		return () => {
			const handlers = this.messageHandlers.get(type);
			if (handlers) {
				handlers.delete(handler);
			}
		};
	}

	// 注册连接状态处理器
	public onStateChange(handler: (state: number) => void) {
		const subscription = this.subject.subscribe(handler);

		// 返回取消注册的函数
		return () => subscription.unsubscribe();
	}

	// 通知连接状态变化
	private emitStateChange(state: number) {
		this.subject.next(state);
	}

	// 发送消息
	public send(message: WebSocketMessage) {
		if (this.ws && this.ws.readyState === WebSocket.OPEN) {
			try {
				const messageWithTimestamp = {
					...message,
					timestamp: Date.now(),
				};
				this.ws.send(JSON.stringify(messageWithTimestamp));
				return true;
			} catch (error) {
				console.error("发送WebSocket消息失败:", error);
				return false;
			}
		} else {
			console.warn("WebSocket未连接，无法发送消息");
			return false;
		}
	}

	// 启动心跳
	private startHeartbeat() {
		this.stopHeartbeat(); // 先清理之前的心跳

		this.heartbeatInterval = setInterval(() => {
			if (this.isConnected) {
				this.send({type: "ping", data: "ping"});

				// 设置心跳超时检测
				this.heartbeatTimeout = setTimeout(() => {
					console.warn("心跳超时，连接可能已断开");
					this.disconnect(CLOSE_REASON.heartbeat_error);
				}, 10 * SECOND); // 5秒超时
			}
		}, 1 * MINUTE); // 每分钟发送一次心跳
	}

	// 停止心跳
	private stopHeartbeat() {
		if (this.heartbeatInterval) {
			clearInterval(this.heartbeatInterval);
			this.heartbeatInterval = null;
		}

		if (this.heartbeatTimeout) {
			clearTimeout(this.heartbeatTimeout);
			this.heartbeatTimeout = null;
		}
	}

	// 处理心跳响应
	private handlePong() {
		if (this.heartbeatTimeout) {
			clearTimeout(this.heartbeatTimeout);
			this.heartbeatTimeout = null;
		}
	}

	// 断开连接
	public disconnect(
		reason: ValueOf<typeof CLOSE_REASON> = CLOSE_REASON.normal,
	) {
		this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自动重连
		this.stopHeartbeat(); // 停止心跳
		if (this.ws) {
			this.ws.close(1000, reason);
			this.ws = null;
		}
		this.emitStateChange(WebSocket.CLOSED);
	}

	// 重置重连计数
	public resetReconnectAttempts() {
		this.reconnectAttempts = 0;
	}
}

// 全局WebSocket管理器实例
export const ws = new Ws();

export type {Ws as WebSocketManager};
