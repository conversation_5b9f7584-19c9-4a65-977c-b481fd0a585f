import Dexie, {type Table} from "dexie";
import {getIdentifier} from "./auth";
import {defaultStore} from "@/jotai/stores/default";
import {dbAtom} from "@/jotai/atoms/db";

// 消息接口
export interface Message {
	id?: number;
	chatId: string;
	messageId: string;
	content: string;
	type: "text" | "image" | "file" | "audio" | "video" | "system";
	sender: string;
	senderNickname?: string;
	timestamp: number;
	status: "sending" | "sent" | "delivered" | "read" | "failed";
	replyTo?: string;
	isDeleted?: boolean;
	deletedAt?: number;
}

// 聊天接口
export interface Chat {
	id?: number;
	chatId: string;
	name: string;
	type: "private" | "group";
	avatar?: string;
	description?: string;
	memberCount?: number;
	lastMessage?: string;
	lastMessageTime?: number;
	unreadCount: number;
	isMuted: boolean;
	isPinned: boolean;
	createdAt: number;
	updatedAt: number;
}

// 联系人接口
export interface Contact {
	id?: number;
	userId: string;
	account: string;
	nickname: string;
	avatar?: string;
	email?: string;
	phone?: string;
	status: "online" | "offline" | "away" | "busy";
	lastSeen?: number;
	isBlocked: boolean;
	isFriend: boolean;
	addedAt: number;
	updatedAt: number;
}

// 生成数据库名称
// 将特殊字符替换为下划线，确保数据库名称有效
const getDBName = (userIdentifier: string) =>
	`liyu_${userIdentifier.replace(/[^a-zA-Z0-9]/g, "_")}`;

let userIdentifier: string | null = null;

const initDB = (db: Dexie) => {
	db.version(1).stores({
		messages: "++id, chatId, messageId, sender, timestamp, status",
		chats: "++id, chatId, type, lastMessageTime, isPinned",
		contacts: "++id, userId, account, nickname, status, isFriend",
	});
};

export const openDB = async (account: string, serverUrl: string) => {
	// 生成用户唯一标识符
	const identifier = getIdentifier(account, serverUrl);
	const db = getDB();

	if (db) {
		if (identifier === userIdentifier) {
			return db;
		}

		closeDB();
	}

	// 如果当前数据库不匹配，关闭旧数据库并打开新数据库
	const name = getDBName(identifier);
	const newDB = new Dexie(name);
	initDB(newDB);
	await newDB.open();
	userIdentifier = identifier;
	setDB(newDB);

	console.log(`数据库已打开: ${name} (用户: ${userIdentifier})`);

	return db;
};

export const getDB = () => defaultStore.get(dbAtom);

const setDB = (db: Dexie | null) => defaultStore.set(dbAtom, db);

export const closeDB = () => {
	const db = getDB();
	if (!db) {
		return;
	}

	db.close();
	setDB(null);
	userIdentifier = null;
	console.log("数据库已关闭");
};

export const deleteDB = async (account: string, serverUrl: string) => {
	const identifier = getIdentifier(account, serverUrl);
	const dbName = getDBName(identifier);

	// 如果是当前打开的数据库，先关闭
	if (userIdentifier === identifier) {
		closeDB();
	}

	// 删除数据库
	const toCloseDB = new Dexie(dbName);
	await toCloseDB.delete();
	console.log(`数据库已删除: ${dbName} (用户: ${identifier})`);
};
