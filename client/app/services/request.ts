import type {Member} from "@/core/member";
import {buildApiUrl, type RegisterRequest} from "./auth";

export const loginRequest = async (
	url: string,
	account: string,
	hashedPassword: string,
) => {
	const response = await fetch(buildApiUrl(url, "/auth/login"), {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({
			account: account,
			password: hashedPassword,
		}),
	});

	const json: LiyuResponse<{
		user: Member;
		accessToken: string;
		refreshToken: string;
		expiresIn: number;
		websocketUrl: string;
	}> = await response.json();
	return json;
};

export const validateRequest = async (url: string, token: string) => {
	const response = await fetch(buildApiUrl(url, "/auth/validate"), {
		method: "GET",
		headers: {
			Authorization: `Bearer ${token}`,
		},
	});

	const json: LiyuResponse<Member> = await response.json();
	return json;
};

export const registerRequest = async (request: RegisterRequest) => {
	const {serverUrl, ...newUser} = request;

	// 发送注册请求
	const response = await fetch(buildApiUrl(serverUrl, "/auth/register"), {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(newUser),
	});

	const json: LiyuResponse = await response.json();
	return json;
};
