import {ipc<PERSON><PERSON><PERSON>} from "electron";
import {
	DELETE_USER,
	GET_COOKIE,
	GET_LOGIN_ITEM_SETTINGS,
	GET_USER,
	GET_USERS,
	REMOVE_COOKIE,
	SET_COOKIE,
	SET_LOGIN_ITEM_SETTINGS,
	SET_USER,
} from "@/constants/remote-events";

const electronAPI = {
	// 自启动相关 API
	getLoginItemSettings: (options?: Electron.LoginItemSettingsOptions) =>
		ipcRenderer.invoke(
			GET_LOGIN_ITEM_SETTINGS,
			options,
		) as Promise<Electron.LoginItemSettings>,
	setLoginItemSettings: (settings: Electron.Settings) =>
		ipcRenderer.invoke(SET_LOGIN_ITEM_SETTINGS, settings) as Promise<void>,

	// Cookie 管理 API
	setCookie: (details: Electron.CookiesSetDetails) =>
		ipcRenderer.invoke(SET_COOKIE, details) as Promise<boolean>,
	getCookie: (filter: Electron.CookiesGetFilter) =>
		ipcRenderer.invoke(GET_COOKIE, filter) as Promise<string | null>,
	removeCookie: (url: string, name: string) =>
		ipcRenderer.invoke(REMOVE_COOKIE, url, name) as Promise<boolean>,
	getUser: (account: string) =>
		ipcRenderer.invoke(GET_USER, account) as Promise<string | null>,
	getUsers: () => ipcRenderer.invoke(GET_USERS) as Promise<string[]>,
	setUser: (account: string, value: string) =>
		ipcRenderer.invoke(SET_USER, account, value) as Promise<boolean>,
	deleteUser: (account: string) =>
		ipcRenderer.invoke(DELETE_USER, account) as Promise<boolean>,
};

export default electronAPI;

declare global {
	interface Window {
		electronAPI: typeof electronAPI;
	}
}
