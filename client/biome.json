{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["app/**/*.ts", "app/**/*.tsx", "main/**/*.ts", "preload/**/*.ts", "!app/components/ui/*.tsx"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noUselessElse": "error"}}}, "javascript": {"formatter": {"bracketSpacing": false}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}