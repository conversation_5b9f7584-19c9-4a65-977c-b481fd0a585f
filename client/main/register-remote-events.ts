import {app, ipcMain, session} from "electron";
import {
	DELETE_USER,
	GET_COOKIE,
	GET_LOGIN_ITEM_SETTINGS,
	GET_USER,
	GET_USERS,
	REMOVE_COOKIE,
	SET_COOKIE,
	SET_LOGIN_ITEM_SETTINGS,
	SET_USER,
} from "@/constants/remote-events";

const registerRemoteEvents = () => {
	ipcMain.handle(
		SET_COOKIE,
		async (_event, details: Electron.CookiesSetDetails) => {
			try {
				await session.defaultSession.cookies.set(details);
				return true;
			} catch (error) {
				console.error("Failed to set auth cookie:", error);
				return false;
			}
		},
	);

	ipcMain.handle(
		GET_COOKIE,
		async (_event, filter: Electron.CookiesGetFilter) => {
			try {
				const cookies = await session.defaultSession.cookies.get(filter);
				return cookies.length > 0 ? cookies[0].value : null;
			} catch (error) {
				console.error("Failed to get auth cookie:", error);
				return null;
			}
		},
	);

	ipcMain.handle(REMOVE_COOKIE, async (_event, url: string, name: string) => {
		try {
			await session.defaultSession.cookies.remove(url, name);
			return true;
		} catch (error) {
			console.error("Failed to remove auth cookie:", error);
			return false;
		}
	});

	ipcMain.handle(
		GET_LOGIN_ITEM_SETTINGS,
		(_event, options?: Electron.LoginItemSettingsOptions) => {
			return app.getLoginItemSettings(options);
		},
	);

	ipcMain.handle(
		SET_LOGIN_ITEM_SETTINGS,
		(_event, settings: Electron.Settings) => app.setLoginItemSettings(settings),
	);

	ipcMain.handle(SET_USER, async (_event, account: string, value: string) => {
		try {
			await session.defaultSession.cookies.set({
				url: "liyu://users",
				name: account,
				value,
				httpOnly: true,
				expirationDate: new Date("2099-12-31").getTime(),
			});
			return true;
		} catch {
			return false;
		}
	});

	ipcMain.handle(GET_USERS, async (_event) => {
		const cookies = await session.defaultSession.cookies.get({
			url: "liyu://users",
		});
		return cookies.map((cookie) => cookie.value);
	});

	ipcMain.handle(GET_USER, async (_event, account: string) => {
		const cookies = await session.defaultSession.cookies.get({
			url: "liyu://users",
			name: account,
		});
		return cookies.length > 0 ? cookies[0].value : null;
	});

	ipcMain.handle(DELETE_USER, async (_event, account: string) => {
		try {
			session.defaultSession.cookies.remove("liyu://users", account);
			return true;
		} catch {
			return false;
		}
	});
};

export default registerRemoteEvents;
