---
type: "always_apply"
---

本项目是一个即时通讯软件，服务器由 gin + gorm 实现，客户端由 electron + reactjs 实现，数据库使用 mysql。

## 客户端 client/

1. 路由使用 react router 7 实现，该版本不需要安装 react-router-dom，所需要的变量直接从 react-router 中导入
2. 状态管理使用 jotai 来替换 react context
3. UI 组件使用 shadcn/ui + tailwindcss 实现
4. 数据库缓存使用 dexie.js 实现，应该使用 dexie-react-hooks 将 UI 界面与数据库关联
5. i18next 实现国际化，当前只需要支持简体中文
6. 当前项目配置了 biome 工具，可运行 `npm run format`、`npm run check`、`npm run lint` 来格式化或者检查代码
7. 每个文件的最大行数不应该超过 300 行，应该对代码进行合理的组织和拆分

## 服务器 server/

1. 每个文件的最大行数不应该超过 300 行，应该对代码进行合理的组织和拆分
