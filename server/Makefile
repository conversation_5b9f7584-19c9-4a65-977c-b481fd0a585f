# 即时通讯服务器 Makefile

# 变量定义
BINARY_NAME=liyu-server
MAIN_FILE=main.go
CONFIG_FILE=config.yaml

# Go相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建标志
BUILD_FLAGS=-ldflags="-s -w"
BUILD_DIR=./build

.PHONY: all build clean test deps run dev help

# 默认目标
all: deps build

# 构建项目
build:
	@echo "正在构建项目..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_FILE)
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 构建多平台版本
build-all: build-linux build-windows build-darwin

build-linux:
	@echo "构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_FILE)

build-windows:
	@echo "构建Windows版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_FILE)

build-darwin:
	@echo "构建macOS版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_FILE)

# 安装依赖
deps:
	@echo "正在安装依赖..."
	$(GOMOD) tidy
	$(GOMOD) download

# 运行项目
run:
	@echo "正在启动服务器..."
	$(GOCMD) run $(MAIN_FILE)

# 开发模式运行（带热重载，需要安装air）
dev:
	@if command -v air > /dev/null; then \
		echo "使用air进行热重载开发..."; \
		air; \
	else \
		echo "air未安装，使用普通模式运行..."; \
		echo "提示: 可以通过 'go install github.com/cosmtrek/air@latest' 安装air"; \
		$(MAKE) run; \
	fi

# 运行测试
test:
	@echo "正在运行测试..."
	$(GOTEST) -v ./...

# 运行基准测试
bench:
	@echo "正在运行基准测试..."
	$(GOTEST) -bench=. -benchmem ./...

# 代码格式化
fmt:
	@echo "正在格式化代码..."
	$(GOCMD) fmt ./...

# 代码检查
vet:
	@echo "正在进行代码检查..."
	$(GOCMD) vet ./...

# 清理构建文件
clean:
	@echo "正在清理构建文件..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -rf logs/*
	@echo "清理完成"

# 创建必要的目录
setup:
	@echo "正在创建必要的目录..."
	mkdir -p logs
	mkdir -p uploads
	mkdir -p $(BUILD_DIR)
	@echo "目录创建完成"

# 检查配置文件
check-config:
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "错误: 配置文件 $(CONFIG_FILE) 不存在"; \
		exit 1; \
	else \
		echo "✓ 配置文件检查通过"; \
	fi

# 安装开发工具
install-tools:
	@echo "正在安装开发工具..."
	$(GOGET) github.com/cosmtrek/air@latest
	$(GOGET) golang.org/x/tools/cmd/goimports@latest
	@echo "开发工具安装完成"

# 生成API文档（如果使用swag）
docs:
	@if command -v swag > /dev/null; then \
		echo "正在生成API文档..."; \
		swag init; \
	else \
		echo "swag未安装，跳过文档生成"; \
		echo "提示: 可以通过 'go install github.com/swaggo/swag/cmd/swag@latest' 安装swag"; \
	fi

# Docker相关
docker-build:
	@echo "正在构建Docker镜像..."
	docker build -t liyu-server .

docker-run:
	@echo "正在运行Docker容器..."
	docker run -p 8080:8080 -p 8081:8081 liyu-server

# 帮助信息
help:
	@echo "可用的命令:"
	@echo "  make build        - 构建项目"
	@echo "  make build-all    - 构建所有平台版本"
	@echo "  make deps         - 安装依赖"
	@echo "  make run          - 运行项目"
	@echo "  make dev          - 开发模式运行（热重载）"
	@echo "  make test         - 运行测试"
	@echo "  make bench        - 运行基准测试"
	@echo "  make fmt          - 格式化代码"
	@echo "  make vet          - 代码检查"
	@echo "  make clean        - 清理构建文件"
	@echo "  make setup        - 创建必要的目录"
	@echo "  make check-config - 检查配置文件"
	@echo "  make install-tools- 安装开发工具"
	@echo "  make docs         - 生成API文档"
	@echo "  make docker-build - 构建Docker镜像"
	@echo "  make docker-run   - 运行Docker容器"
	@echo "  make help         - 显示帮助信息"
