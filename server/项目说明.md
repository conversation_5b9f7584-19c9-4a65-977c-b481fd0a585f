# 即时通讯服务器项目说明

## 项目概述

这是一个基于 Go Gin 框架开发的即时通讯服务器，支持 HTTP/HTTPS 和 WebSocket/WSS 双协议，使用 MySQL 数据库存储数据，支持多端登录。

## 已实现功能

### ✅ 核心功能
1. **配置文件管理** - 完整的 YAML 配置文件，支持数据库、服务器、SSL 等配置
2. **数据库模型** - 基于 SQL 设计的完整 GORM 模型，包括用户、会话、消息等表
3. **服务器架构** - 双服务器架构，HTTP 和 WebSocket 分离部署
4. **数据库连接** - 完整的数据库连接池配置和自动迁移
5. **WebSocket 管理** - 完整的 WebSocket 连接管理器，支持多端登录
6. **JWT 认证** - 完整的 JWT 令牌生成、验证和刷新机制
7. **用户认证** - 用户注册、登录、资料管理等 API
8. **心跳检测** - WebSocket 连接心跳检测和超时处理
9. **优雅关闭** - 服务器优雅关闭机制

### 🚧 待完善功能
1. **消息发送与接收** - WebSocket 消息处理逻辑（框架已搭建）
2. **会话管理** - 创建会话、加入会话等功能（API 框架已搭建）
3. **文件上传** - 图片、文件、音频、视频上传（API 框架已搭建）
4. **消息推送** - 离线消息推送机制

## 项目结构

```
server/
├── auth/              # JWT 认证模块
│   └── jwt.go         # JWT 令牌生成和验证
├── config/            # 配置管理模块
│   └── config.go      # 配置文件解析和管理
├── database/          # 数据库操作模块
│   └── database.go    # 数据库连接和基础操作
├── handlers/          # HTTP 处理器
│   ├── auth.go        # 认证相关 API
│   └── common.go      # 通用 API（健康检查、状态等）
├── middleware/        # 中间件
│   └── auth.go        # JWT 认证中间件
├── models/            # 数据模型
│   └── models.go      # GORM 数据模型定义
├── routes/            # 路由配置
│   └── routes.go      # API 路由设置
├── utils/             # 工具函数
│   └── response.go    # 统一响应格式
├── websocket/         # WebSocket 模块
│   ├── manager.go     # WebSocket 连接管理器
│   └── handler.go     # WebSocket 消息处理
├── config.yaml        # 配置文件示例
├── liyu.sql      # 数据库建表脚本
├── main.go            # 主程序入口
├── go.mod             # Go 模块文件
├── Makefile           # 构建脚本
├── start.sh           # Linux/Mac 启动脚本
├── start.bat          # Windows 启动脚本
├── test_client.html   # WebSocket 测试客户端
└── README.md          # 详细使用说明
```

## 技术栈

- **后端框架**: Go 1.21+ + Gin
- **数据库**: MySQL 8.0+ + GORM
- **WebSocket**: Gorilla WebSocket
- **认证**: JWT (golang-jwt/jwt)
- **配置**: YAML
- **密码加密**: bcrypt

## 快速开始

### 1. 环境准备
```bash
# 确保已安装 Go 1.21+ 和 MySQL 8.0+
go version
mysql --version
```

### 2. 数据库设置
```sql
-- 创建数据库
CREATE DATABASE liyu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入表结构
mysql -u root -p liyu < liyu.sql
```

### 3. 配置修改
编辑 `config.yaml` 文件，修改数据库连接信息：
```yaml
database:
  mysql:
    host: "localhost"
    port: 3306
    username: "your_username"
    password: "your_password"
    database: "liyu"
```

### 4. 启动服务器

**方式一：使用启动脚本**
```bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
start.bat
```

**方式二：使用 Makefile**
```bash
make deps    # 安装依赖
make run     # 运行服务器
```

**方式三：直接运行**
```bash
go mod tidy
go run main.go
```

### 5. 测试连接
- HTTP 服务器：http://localhost:8080
- WebSocket 服务器：ws://localhost:8081
- 健康检查：http://localhost:8080/api/v1/health
- 服务器状态：http://localhost:8080/api/v1/status

## 测试客户端

项目包含一个完整的 HTML 测试客户端 `test_client.html`，可以：
1. 测试用户注册和登录
2. 建立 WebSocket 连接
3. 发送认证消息
4. 测试心跳机制
5. 发送和接收消息

直接在浏览器中打开 `test_client.html` 即可使用。

## API 接口

### 认证接口
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌

### 用户接口
- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料

### 系统接口
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/status` - 服务器状态

## WebSocket 协议

### 连接流程
1. 建立 WebSocket 连接：`ws://localhost:8081/ws`
2. 发送认证消息
3. 开始消息通信

### 消息格式
```json
{
  "type": "message_type",
  "data": {},
  "timestamp": **********,
  "message_id": "uuid"
}
```

### 支持的消息类型
- `auth` - 用户认证
- `heartbeat` - 心跳检测
- `send_message` - 发送消息（待完善）
- `join_chat` - 加入会话（待完善）
- `leave_chat` - 离开会话（待完善）

## 部署说明

### 开发环境
```bash
make dev  # 使用热重载（需要安装 air）
```

### 生产环境
```bash
# 构建
make build

# 运行
./build/liyu-server
```

### Docker 部署
```bash
make docker-build
make docker-run
```

## 下一步开发建议

1. **完善消息功能**
   - 实现 `handleSendMessage` 函数
   - 添加消息存储到数据库
   - 实现消息转发给会话成员

2. **完善会话管理**
   - 实现创建会话 API
   - 实现加入/离开会话功能
   - 实现会话成员管理

3. **添加文件上传**
   - 实现文件上传处理
   - 添加文件类型验证
   - 实现文件存储管理

4. **优化性能**
   - 添加 Redis 缓存
   - 实现消息队列
   - 添加数据库索引优化

5. **增强安全性**
   - 添加 CORS 配置
   - 实现 API 限流
   - 添加输入验证

## 许可证

MIT License

---

**项目状态**: 核心框架已完成，可以正常运行，支持用户认证和 WebSocket 连接管理。消息发送、会话管理等业务功能的框架已搭建，需要进一步完善具体实现。
