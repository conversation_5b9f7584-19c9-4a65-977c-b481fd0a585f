#!/bin/bash

# 即时通讯服务器启动脚本

echo "=== 即时通讯服务器启动脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go 1.21+"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | grep -o 'go[0-9]\+\.[0-9]\+' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "错误: Go版本过低，需要Go $REQUIRED_VERSION+，当前版本: $GO_VERSION"
    exit 1
fi

echo "✓ Go环境检查通过: $(go version)"

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "错误: 未找到配置文件 config.yaml"
    echo "请确保配置文件存在并正确配置数据库连接信息"
    exit 1
fi

echo "✓ 配置文件检查通过"

# 安装依赖
echo "正在安装依赖..."
if ! go mod tidy; then
    echo "错误: 依赖安装失败"
    exit 1
fi

echo "✓ 依赖安装完成"

# 创建必要的目录
mkdir -p logs
mkdir -p uploads

echo "✓ 目录创建完成"

# 检查数据库连接（可选）
echo "提示: 请确保MySQL数据库已启动并且配置正确"
echo "提示: 请确保已执行数据库脚本: mysql -u username -p database_name < liyu.sql"

# 启动服务器
echo ""
echo "正在启动服务器..."
echo "HTTP服务器将启动在: http://localhost:8080"
echo "WebSocket服务器将启动在: ws://localhost:8081"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 运行服务器
go run main.go
