# WebSocket 通讯协议 API 文档

## 概述

本文档描述了即时通讯系统的WebSocket通讯协议，包括创建会话和收发消息的完整流程。

## 连接建立

### WebSocket连接地址
```
ws://localhost:8081/api/ws?token={JWT_TOKEN}&device_id={DEVICE_ID}
```

### 参数说明
- `token`: JWT认证令牌（可选，未认证用户只能接收公开消息）
- `device_id`: 设备标识符（可选，默认为"default"）

## 消息格式

所有WebSocket消息都使用JSON格式，基本结构如下：

```json
{
  "id": "消息ID，用于请求响应匹配",
  "type": "消息类型",
  "data": "消息数据对象",
  "timestamp": "Unix时间戳",
  "error": "错误信息（仅错误响应包含）"
}
```

## 客户端发送的消息类型

### 1. 创建群聊 (create_chat)

创建一个新的群聊会话。

**请求格式：**
```json
{
  "id": "req_001",
  "type": "create_chat",
  "data": {
    "type": 2,
    "name": "群聊名称",
    "avatar": "群聊头像URL（可选）",
    "description": "群聊描述（可选）",
    "member_ids": [1001, 1002, 1003]
  },
  "timestamp": 1640995200
}
```

**响应格式：**
```json
{
  "id": "req_001",
  "type": "success",
  "data": {
    "chat": {
      "id": 1,
      "type": 2,
      "name": "群聊名称",
      "avatar": null,
      "description": null,
      "owner_id": 1001,
      "status": 1,
      "created_at": "2023-01-01T00:00:00Z"
    },
    "members": [
      {
        "chat_id": 1,
        "user_id": 1001,
        "role": 1,
        "joined_at": "2023-01-01T00:00:00Z"
      }
    ]
  },
  "timestamp": 1640995201
}
```

### 2. 发送消息 (send_message)

发送消息到指定聊天或创建私聊。

**发送到现有聊天：**
```json
{
  "id": "req_002",
  "type": "send_message",
  "data": {
    "chat_id": 1,
    "content": "Hello, World!",
    "content_type": 1,
    "reply_to_msg_id": 123,
    "extra": {
      "mentions": [1002]
    }
  },
  "timestamp": 1640995300
}
```

**创建私聊并发送消息：**
```json
{
  "id": "req_003",
  "type": "send_message",
  "data": {
    "to_user_id": 1002,
    "content": "私聊消息",
    "content_type": 1
  },
  "timestamp": 1640995400
}
```

**内容类型说明：**
- `1`: 文本消息
- `2`: 图片消息
- `3`: 文件消息
- `4`: 语音消息
- `5`: 视频消息

### 3. 加入聊天房间 (join_chat)

将当前连接加入指定聊天房间，以接收该聊天的实时消息。

```json
{
  "id": "req_004",
  "type": "join_chat",
  "data": {
    "chat_id": 1
  },
  "timestamp": 1640995500
}
```

### 4. 离开聊天房间 (leave_chat)

将当前连接从指定聊天房间移除。

```json
{
  "id": "req_005",
  "type": "leave_chat",
  "data": {
    "chat_id": 1
  },
  "timestamp": 1640995600
}
```

### 5. 获取聊天列表 (get_chat_list)

获取用户的聊天列表。

```json
{
  "id": "req_006",
  "type": "get_chat_list",
  "data": {
    "page": 1,
    "page_size": 20
  },
  "timestamp": 1640995700
}
```

### 6. 获取消息列表 (get_messages)

获取指定聊天的消息列表。

```json
{
  "id": "req_007",
  "type": "get_messages",
  "data": {
    "chat_id": 1,
    "page": 1,
    "page_size": 20
  },
  "timestamp": 1640995800
}
```

### 7. 撤回消息 (recall_message)

撤回已发送的消息（2分钟内）。

```json
{
  "id": "req_008",
  "type": "recall_message",
  "data": {
    "message_id": 123
  },
  "timestamp": 1640995900
}
```

### 8. 编辑消息 (edit_message)

编辑已发送的消息（5分钟内）。

```json
{
  "id": "req_009",
  "type": "edit_message",
  "data": {
    "message_id": 123,
    "content": "编辑后的内容",
    "extra": {
      "edited": true
    }
  },
  "timestamp": 1640996000
}
```

### 9. 正在输入 (typing)

通知其他用户正在输入。

```json
{
  "id": "req_010",
  "type": "typing",
  "data": {
    "chat_id": 1
  },
  "timestamp": 1640996100
}
```

### 10. 心跳 (heartbeat)

保持连接活跃。

```json
{
  "id": "req_011",
  "type": "heartbeat",
  "data": {},
  "timestamp": 1640996200
}
```

## 服务端推送的消息类型

### 1. 聊天已创建 (chat_created)

通知聊天成员新聊天已创建。

```json
{
  "type": "chat_created",
  "data": {
    "chat": {
      "id": 1,
      "type": 2,
      "name": "新群聊",
      "owner_id": 1001
    },
    "members": [...]
  },
  "timestamp": 1640996300
}
```

### 2. 收到新消息 (message_received)

通知聊天成员收到新消息。

```json
{
  "type": "message_received",
  "data": {
    "message": {
      "id": 123,
      "chat_id": 1,
      "sender_id": 1001,
      "content": "Hello!",
      "content_type": 1,
      "seq": 1,
      "created_at": "2023-01-01T00:00:00Z"
    }
  },
  "timestamp": 1640996400
}
```

### 3. 消息已撤回 (message_recalled)

通知聊天成员消息已被撤回。

```json
{
  "type": "message_recalled",
  "data": {
    "message_id": 123,
    "chat_id": 1
  },
  "timestamp": 1640996500
}
```

### 4. 消息已编辑 (message_edited)

通知聊天成员消息已被编辑。

```json
{
  "type": "message_edited",
  "data": {
    "message": {
      "id": 123,
      "content": "编辑后的内容",
      "updated_at": "2023-01-01T00:01:00Z"
    }
  },
  "timestamp": 1640996600
}
```

### 5. 用户加入 (user_joined)

通知聊天成员有新用户加入。

```json
{
  "type": "user_joined",
  "data": {
    "chat_id": 1,
    "user": {
      "user_id": 1003,
      "nickname": "新成员"
    }
  },
  "timestamp": 1640996700
}
```

### 6. 用户离开 (user_left)

通知聊天成员有用户离开。

```json
{
  "type": "user_left",
  "data": {
    "chat_id": 1,
    "user_id": 1003
  },
  "timestamp": 1640996800
}
```

### 7. 用户正在输入 (user_typing)

通知聊天成员有用户正在输入。

```json
{
  "type": "user_typing",
  "data": {
    "chat_id": 1,
    "user": {
      "user_id": 1002
    }
  },
  "timestamp": 1640996900
}
```

### 8. 错误响应 (error)

返回错误信息。

```json
{
  "id": "req_001",
  "type": "error",
  "error": "权限不足",
  "timestamp": 1640997000
}
```

### 9. 成功响应 (success)

返回成功响应。

```json
{
  "id": "req_001",
  "type": "success",
  "data": {
    "message": "操作成功"
  },
  "timestamp": 1640997100
}
```

## 业务流程

### 创建会话流程

1. **群聊创建**：
   - 客户端发送 `create_chat` 消息
   - 服务端验证权限和参数
   - 创建聊天记录和成员关系
   - 初始化消息序号计数器
   - 向所有成员推送 `chat_created` 消息

2. **私聊懒创建**：
   - 客户端发送 `send_message` 消息（指定 `to_user_id`）
   - 服务端检查是否存在私聊
   - 如不存在则自动创建私聊
   - 发送消息并推送给双方

### 消息收发流程

1. **发送消息**：
   - 客户端发送 `send_message` 消息
   - 服务端验证用户权限和聊天成员身份
   - 在事务中创建消息记录并更新序号计数器
   - 更新聊天的最后消息信息
   - 向聊天成员推送 `message_received` 消息

2. **接收消息**：
   - 客户端加入聊天房间（`join_chat`）
   - 服务端实时推送该聊天的新消息
   - 客户端处理 `message_received` 消息

## 错误处理

常见错误码和处理方式：

- **权限不足**：用户不是聊天成员或没有操作权限
- **参数错误**：请求参数格式错误或缺少必要参数
- **资源不存在**：聊天或消息不存在
- **操作限制**：如撤回时间超限、编辑时间超限等

## 最佳实践

1. **连接管理**：
   - 实现自动重连机制
   - 定期发送心跳保持连接
   - 处理连接断开和重连后的消息同步

2. **消息处理**：
   - 使用消息ID进行请求响应匹配
   - 实现消息去重机制
   - 处理消息发送失败的重试逻辑

3. **性能优化**：
   - 合理使用聊天房间机制
   - 及时离开不需要的聊天房间
   - 实现消息分页加载

4. **安全考虑**：
   - 验证用户权限
   - 防止消息重放攻击
   - 限制消息发送频率
