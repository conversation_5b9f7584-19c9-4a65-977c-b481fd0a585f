# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build output
/build/
/dist/
liyu-server
liyu-server.exe

# Logs
/logs/
*.log

# Uploads
/uploads/
/static/uploads/

# Configuration files (keep examples)
config.yaml
config.yml
config.json
!config.example.yaml
!config.example.yml

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
/tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# SSL certificates
*.pem
*.key
*.crt
/certs/

# Docker
.dockerignore
Dockerfile.dev

# Test coverage
coverage.out
coverage.html

# Air (hot reload) temporary files
tmp/
.air.toml

# Backup files
*.bak
*.backup

# Documentation build
/docs/build/

# Node.js (if using for frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using for scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Local development
.local/
local.yaml
local.yml
