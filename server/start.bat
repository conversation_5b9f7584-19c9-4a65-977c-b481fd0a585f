@echo off
chcp 65001 >nul

echo === 即时通讯服务器启动脚本 ===

REM 检查Go环境
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go 1.21+
    pause
    exit /b 1
)

echo ✓ Go环境检查通过
go version

REM 检查配置文件
if not exist "config.yaml" (
    echo 错误: 未找到配置文件 config.yaml
    echo 请确保配置文件存在并正确配置数据库连接信息
    pause
    exit /b 1
)

echo ✓ 配置文件检查通过

REM 安装依赖
echo 正在安装依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo ✓ 依赖安装完成

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo ✓ 目录创建完成

REM 提示信息
echo.
echo 提示: 请确保MySQL数据库已启动并且配置正确
echo 提示: 请确保已执行数据库脚本: mysql -u username -p database_name ^< liyu.sql
echo.

REM 启动服务器
echo 正在启动服务器...
echo HTTP服务器将启动在: http://localhost:8080
echo WebSocket服务器将启动在: ws://localhost:8081
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 运行服务器
go run main.go

pause
