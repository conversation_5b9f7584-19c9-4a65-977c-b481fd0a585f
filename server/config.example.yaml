# 即时通讯服务器配置文件示例
# 复制此文件为 config.yaml 并修改相应配置

# 服务器配置
server:
  # HTTP服务器配置
  http:
    host: "0.0.0.0"
    port: 8080
    # 是否启用HTTPS
    enable_ssl: false
    # SSL证书配置（当enable_ssl为true时生效）
    ssl:
      cert_file: "./certs/server.crt"
      key_file: "./certs/server.key"

  # WebSocket服务器配置
  websocket:
    host: "0.0.0.0"
    port: 8081
    # 是否启用WSS
    enable_ssl: false
    # SSL证书配置（当enable_ssl为true时生效）
    ssl:
      cert_file: "./certs/server.crt"
      key_file: "./certs/server.key"
    # WebSocket配置
    read_buffer_size: 1024
    write_buffer_size: 1024
    # 心跳检测间隔（秒）
    heartbeat_interval: 30
    # 连接超时时间（秒）
    connection_timeout: 300

# 数据库配置
database:
  # MySQL数据库配置
  mysql:
    host: "localhost"
    port: 3306
    username: "tintin"
    password: "tintin"
    database: "liyu"
    charset: "utf8mb4"
    # 连接池配置
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600 # 秒
    # 是否启用日志
    log_level: "info" # silent, error, warn, info

# 日志配置
log:
  # 日志级别: debug, info, warn, error
  level: "info"
  # 日志格式: json, text
  format: "json"
  # 日志输出: stdout, file
  output: "stdout"
  # 当output为file时的文件路径
  file_path: "./logs/liyu.log"
  # 日志文件最大大小（MB）
  max_size: 100
  # 保留的日志文件数量
  max_backups: 10
  # 日志文件保留天数
  max_age: 30

# JWT配置
jwt:
  # JWT密钥（生产环境请使用强密钥）
  secret: "liyu_jwt_secret_key_2024_change_me_in_production"
  # Token过期时间（小时）
  expires_hours: 24
  # 刷新Token过期时间（小时）
  refresh_expires_hours: 168 # 7天

# Redis配置（可选，用于缓存和分布式锁）
redis:
  # 是否启用Redis
  enabled: false
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  # 连接池配置
  pool_size: 10
  min_idle_conns: 5

# 文件上传配置
upload:
  # 上传文件存储路径
  path: "./uploads"
  # 最大文件大小（MB）
  max_size: 100
  # 允许的文件类型
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "video/mp4"
    - "video/webm"
    - "audio/mp3"
    - "audio/wav"
    - "audio/ogg"
    - "application/pdf"
    - "application/msword"
    - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    - "application/vnd.ms-excel"
    - "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

# 消息配置
message:
  # 单次拉取消息的最大数量
  max_pull_count: 100
  # 消息内容最大长度（字符）
  max_content_length: 10000
  # 是否启用消息加密
  enable_encryption: false
  # 消息撤回时间限制（分钟）
  recall_time_limit: 2

# 开发模式配置
development:
  # 是否启用开发模式
  enabled: true
  # 是否启用API文档
  enable_swagger: true
  # 是否启用跨域
  enable_cors: true
