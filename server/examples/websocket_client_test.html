<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 聊天测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            flex: 1;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #f3e5f5;
        }
        .message.system {
            background-color: #fff3e0;
            font-style: italic;
        }
        input, textarea, button {
            margin: 5px 0;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        button {
            background-color: #2196f3;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.connected {
            background-color: #c8e6c9;
            color: #2e7d32;
        }
        .status.disconnected {
            background-color: #ffcdd2;
            color: #c62828;
        }
    </style>
</head>
<body>
    <h1>WebSocket 聊天测试客户端</h1>
    
    <div class="container">
        <!-- 连接面板 -->
        <div class="panel">
            <h3>连接设置</h3>
            <div>
                <label>WebSocket URL:</label><br>
                <input type="text" id="wsUrl" value="ws://localhost:8081/api/ws" style="width: 100%;">
            </div>
            <div>
                <label>Token:</label><br>
                <input type="text" id="token" placeholder="认证Token" style="width: 100%;">
            </div>
            <div>
                <label>设备ID:</label><br>
                <input type="text" id="deviceId" value="web_client_1" style="width: 100%;">
            </div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            
            <div id="status" class="status disconnected">未连接</div>
        </div>

        <!-- 聊天操作面板 -->
        <div class="panel">
            <h3>聊天操作</h3>
            
            <h4>创建群聊</h4>
            <div>
                <input type="text" id="chatName" placeholder="群聊名称">
                <input type="text" id="memberIds" placeholder="成员ID列表 (逗号分隔)">
                <button onclick="createGroupChat()">创建群聊</button>
            </div>

            <h4>发送消息</h4>
            <div>
                <input type="number" id="chatId" placeholder="聊天ID">
                <input type="number" id="toUserId" placeholder="私聊用户ID (可选)">
                <textarea id="messageContent" placeholder="消息内容" rows="3" style="width: 100%;"></textarea>
                <button onclick="sendMessage()">发送消息</button>
            </div>

            <h4>其他操作</h4>
            <div>
                <button onclick="getChatList()">获取聊天列表</button>
                <button onclick="sendHeartbeat()">发送心跳</button>
            </div>
        </div>
    </div>

    <!-- 消息显示面板 -->
    <div class="panel">
        <h3>消息记录</h3>
        <div id="messages" class="messages"></div>
        <button onclick="clearMessages()">清空消息</button>
    </div>

    <script>
        let ws = null;
        let messageId = 1;

        function connect() {
            const url = document.getElementById('wsUrl').value;
            const token = document.getElementById('token').value;
            const deviceId = document.getElementById('deviceId').value;
            
            let wsUrl = url;
            if (token) {
                wsUrl += `?token=${encodeURIComponent(token)}`;
                if (deviceId) {
                    wsUrl += `&device_id=${encodeURIComponent(deviceId)}`;
                }
            }

            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    updateStatus('connected', '已连接');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    addMessage('system', '连接成功');
                };

                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage('received', `收到: ${JSON.stringify(message, null, 2)}`);
                    } catch (e) {
                        addMessage('received', `收到原始消息: ${event.data}`);
                    }
                };

                ws.onclose = function(event) {
                    updateStatus('disconnected', '连接已断开');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    addMessage('system', `连接断开: ${event.code} ${event.reason}`);
                };

                ws.onerror = function(error) {
                    addMessage('system', `连接错误: ${error}`);
                };

            } catch (error) {
                addMessage('system', `连接失败: ${error.message}`);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }

            const chatId = parseInt(document.getElementById('chatId').value);
            const toUserId = document.getElementById('toUserId').value;
            const content = document.getElementById('messageContent').value;

            if (!content.trim()) {
                alert('请输入消息内容');
                return;
            }

            const message = {
                id: `msg_${messageId++}`,
                type: 'send_message',
                data: {
                    content: content,
                    content_type: 1, // 文本消息
                },
                timestamp: Math.floor(Date.now() / 1000)
            };

            if (chatId) {
                message.data.chat_id = chatId;
            } else if (toUserId) {
                message.data.to_user_id = parseInt(toUserId);
            } else {
                alert('请指定聊天ID或私聊用户ID');
                return;
            }

            ws.send(JSON.stringify(message));
            addMessage('sent', `发送: ${JSON.stringify(message, null, 2)}`);
            document.getElementById('messageContent').value = '';
        }

        function createGroupChat() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }

            const name = document.getElementById('chatName').value;
            const memberIds = document.getElementById('memberIds').value;

            if (!name.trim()) {
                alert('请输入群聊名称');
                return;
            }

            const members = memberIds ? memberIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];

            const message = {
                id: `msg_${messageId++}`,
                type: 'create_chat',
                data: {
                    type: 2, // 群聊
                    name: name,
                    member_ids: members
                },
                timestamp: Math.floor(Date.now() / 1000)
            };

            ws.send(JSON.stringify(message));
            addMessage('sent', `发送: ${JSON.stringify(message, null, 2)}`);
        }

        function getChatList() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }

            const message = {
                id: `msg_${messageId++}`,
                type: 'get_chat_list',
                data: {
                    page: 1,
                    page_size: 20
                },
                timestamp: Math.floor(Date.now() / 1000)
            };

            ws.send(JSON.stringify(message));
            addMessage('sent', `发送: ${JSON.stringify(message, null, 2)}`);
        }

        function sendHeartbeat() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket未连接');
                return;
            }

            const message = {
                id: `msg_${messageId++}`,
                type: 'heartbeat',
                data: {},
                timestamp: Math.floor(Date.now() / 1000)
            };

            ws.send(JSON.stringify(message));
            addMessage('sent', `发送心跳: ${JSON.stringify(message, null, 2)}`);
        }

        function updateStatus(type, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = text;
        }

        function addMessage(type, content) {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<small>${new Date().toLocaleTimeString()}</small><br><pre>${content}</pre>`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus('disconnected', '未连接');
        };

        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
