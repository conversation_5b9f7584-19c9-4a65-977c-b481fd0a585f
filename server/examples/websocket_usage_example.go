package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"

	"liyu/internal/domain/chat"
	"liyu/internal/domain/message"
	"liyu/internal/websocket"
)

// 这个示例展示了如何在业务代码中使用WebSocket服务

func main() {
	// 假设你已经有了数据库连接
	var db *gorm.DB // 这里应该是你的实际数据库连接

	// 创建WebSocket服务
	wsService := websocket.NewWebSocketService(db)

	// 获取聊天和消息服务
	chatService := wsService.GetChatService()
	messageService := wsService.GetMessageService()

	// 示例1：创建群聊
	createGroupChatExample(chatService, wsService)

	// 示例2：发送消息
	sendMessageExample(messageService, wsService)

	// 示例3：处理用户加入/离开
	handleUserJoinLeaveExample(wsService)

	// 示例4：获取在线用户
	getOnlineUsersExample(wsService)
}

// 示例1：创建群聊
func createGroupChatExample(chatService chat.Service, wsService *websocket.WebSocketService) {
	ctx := context.Background()

	// 创建群聊请求
	req := &chat.CreateChatRequest{
		Type:        chat.TypeGroup,
		Name:        stringPtr("技术讨论群"),
		Description: stringPtr("讨论技术问题的群聊"),
		MemberIDs:   []uint64{1001, 1002, 1003}, // 成员ID列表
	}

	// 创建群聊
	newChat, err := chatService.CreateGroupChat(ctx, req, 1001) // 1001是创建者ID
	if err != nil {
		log.Printf("创建群聊失败: %v", err)
		return
	}

	// 获取群聊成员
	members, err := chatService.GetChatMembers(ctx, newChat.ID, 1001)
	if err != nil {
		log.Printf("获取群聊成员失败: %v", err)
		return
	}

	// 通知所有成员群聊已创建
	wsService.NotifyChatCreated(newChat, members)

	fmt.Printf("群聊创建成功: ID=%d, 名称=%s\n", newChat.ID, *newChat.Name)
}

// 示例2：发送消息
func sendMessageExample(messageService message.Service, wsService *websocket.WebSocketService) {
	ctx := context.Background()

	// 发送消息请求
	req := &message.SendMessageRequest{
		ChatID:      1, // 聊天ID
		Content:     stringPtr("Hello, everyone!"),
		ContentType: message.ContentTypeText,
	}

	// 发送消息
	newMessage, err := messageService.SendMessage(ctx, req, 1001) // 1001是发送者ID
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	// 通知聊天成员收到新消息
	wsService.NotifyMessageReceived(newMessage, newMessage.ChatID)

	fmt.Printf("消息发送成功: ID=%d, 序号=%d\n", newMessage.ID, newMessage.Seq)
}

// 示例3：处理用户加入/离开
func handleUserJoinLeaveExample(wsService *websocket.WebSocketService) {
	chatID := uint64(1)
	userID := uint64(1004)

	// 模拟用户信息
	user := map[string]interface{}{
		"user_id":  userID,
		"nickname": "新用户",
		"avatar":   "https://example.com/avatar.jpg",
	}

	// 通知用户加入
	wsService.NotifyUserJoined(chatID, userID, user)
	fmt.Printf("通知用户 %d 加入聊天 %d\n", userID, chatID)

	// 模拟一段时间后用户离开
	time.Sleep(5 * time.Second)

	// 通知用户离开
	wsService.NotifyUserLeft(chatID, userID)
	fmt.Printf("通知用户 %d 离开聊天 %d\n", userID, chatID)
}

// 示例4：获取在线用户
func getOnlineUsersExample(wsService *websocket.WebSocketService) {
	// 获取所有在线用户
	onlineUsers := wsService.GetOnlineUsers()
	fmt.Printf("当前在线用户: %v\n", onlineUsers)

	// 检查特定用户是否在线
	userID := uint64(1001)
	isOnline := wsService.IsUserOnline(userID)
	fmt.Printf("用户 %d 是否在线: %v\n", userID, isOnline)

	// 获取WebSocket统计信息
	stats := wsService.GetStats()
	fmt.Printf("WebSocket统计: %+v\n", stats)
}

// 示例5：在HTTP API中集成WebSocket通知
func httpAPIIntegrationExample(wsService *websocket.WebSocketService) {
	// 这个示例展示了如何在HTTP API处理器中使用WebSocket服务

	// 假设这是一个HTTP API处理器，用户通过REST API发送消息
	handleSendMessageAPI := func(chatID uint64, senderID uint64, content string) {
		ctx := context.Background()

		// 通过消息服务发送消息
		req := &message.SendMessageRequest{
			ChatID:      chatID,
			Content:     &content,
			ContentType: message.ContentTypeText,
		}

		messageService := wsService.GetMessageService()
		newMessage, err := messageService.SendMessage(ctx, req, senderID)
		if err != nil {
			log.Printf("发送消息失败: %v", err)
			return
		}

		// 通过WebSocket实时通知聊天成员
		wsService.NotifyMessageReceived(newMessage, chatID)

		fmt.Printf("HTTP API发送消息成功，已通过WebSocket推送\n")
	}

	// 调用示例
	handleSendMessageAPI(1, 1001, "通过HTTP API发送的消息")
}

// 示例6：处理消息撤回
func handleMessageRecallExample(messageService message.Service, wsService *websocket.WebSocketService) {
	ctx := context.Background()
	messageID := uint64(123)
	userID := uint64(1001)

	// 撤回消息
	err := messageService.RecallMessage(ctx, messageID, userID)
	if err != nil {
		log.Printf("撤回消息失败: %v", err)
		return
	}

	// 通知聊天成员消息已撤回
	// 注意：这里需要先获取消息信息来得到chatID
	msg, err := messageService.GetMessage(ctx, messageID, userID)
	if err != nil {
		log.Printf("获取消息信息失败: %v", err)
		return
	}

	wsService.NotifyMessageRecalled(messageID, msg.ChatID)
	fmt.Printf("消息 %d 已撤回并通知相关用户\n", messageID)
}

// 示例7：处理消息编辑
func handleMessageEditExample(messageService message.Service, wsService *websocket.WebSocketService) {
	ctx := context.Background()
	messageID := uint64(123)
	userID := uint64(1001)
	newContent := "编辑后的消息内容"

	// 编辑消息
	req := &message.UpdateMessageRequest{
		Content: &newContent,
	}

	updatedMessage, err := messageService.UpdateMessage(ctx, messageID, userID, req)
	if err != nil {
		log.Printf("编辑消息失败: %v", err)
		return
	}

	// 通知聊天成员消息已编辑
	wsService.NotifyMessageEdited(updatedMessage)
	fmt.Printf("消息 %d 已编辑并通知相关用户\n", messageID)
}

// 示例8：私聊懒创建
func privateChatLazyCreationExample(chatService chat.Service, messageService message.Service, wsService *websocket.WebSocketService) {
	ctx := context.Background()
	userID1 := uint64(1001)
	userID2 := uint64(1002)

	// 获取或创建私聊
	privateChat, err := chatService.GetOrCreatePrivateChat(ctx, userID1, userID2)
	if err != nil {
		log.Printf("获取或创建私聊失败: %v", err)
		return
	}

	// 发送私聊消息
	req := &message.SendMessageRequest{
		ChatID:      privateChat.ID,
		Content:     stringPtr("这是一条私聊消息"),
		ContentType: message.ContentTypeText,
	}

	newMessage, err := messageService.SendMessage(ctx, req, userID1)
	if err != nil {
		log.Printf("发送私聊消息失败: %v", err)
		return
	}

	// 通知双方收到新消息
	wsService.NotifyMessageReceived(newMessage, privateChat.ID)

	fmt.Printf("私聊消息发送成功: 聊天ID=%d, 消息ID=%d\n", privateChat.ID, newMessage.ID)
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

// 示例9：完整的聊天流程
func completeChatFlowExample() {
	fmt.Println(`
完整的聊天流程示例：

1. 用户连接WebSocket
   - 客户端连接: ws://localhost:8081/api/ws?token=JWT_TOKEN
   - 服务端验证token并建立连接

2. 创建群聊
   - 客户端发送: {"type": "create_chat", "data": {...}}
   - 服务端创建聊天并初始化序号计数器
   - 推送给所有成员: {"type": "chat_created", "data": {...}}

3. 加入聊天房间
   - 客户端发送: {"type": "join_chat", "data": {"chat_id": 1}}
   - 服务端将连接加入聊天房间

4. 发送消息
   - 客户端发送: {"type": "send_message", "data": {...}}
   - 服务端在事务中创建消息并更新序号
   - 推送给房间内所有成员: {"type": "message_received", "data": {...}}

5. 私聊懒创建
   - 客户端发送: {"type": "send_message", "data": {"to_user_id": 1002, ...}}
   - 服务端检查私聊是否存在，不存在则创建
   - 发送消息并推送给双方

6. 消息操作
   - 撤回: {"type": "recall_message", "data": {"message_id": 123}}
   - 编辑: {"type": "edit_message", "data": {"message_id": 123, "content": "新内容"}}
   - 推送操作结果给相关用户

7. 实时状态
   - 正在输入: {"type": "typing", "data": {"chat_id": 1}}
   - 心跳保活: {"type": "heartbeat", "data": {}}
`)
}
