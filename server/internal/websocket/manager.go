package websocket

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Manager WebSocket连接管理器
type Manager struct {
	// 所有客户端连接 map[clientID]*Client
	clients map[string]*Client
	// 用户的所有连接 map[userID]map[deviceID]*Client
	userConnections map[uint64]map[string]*Client
	// 聊天房间 map[chatID]map[clientID]*Client
	chatRooms map[uint64]map[string]*Client
	// 消息处理器
	messageHandler *MessageHandler
	// 注册新连接
	register chan *Client
	// 注销连接
	unregister chan *Client
	// 广播消息
	broadcast chan *Message
	// 发送消息给指定用户
	sendToUser chan *UserMessage
	// 发送消息给指定会话
	sendToChat chan *ChatMessage
	// 互斥锁
	mutex sync.RWMutex
	// 停止信号
	stopChan chan struct{}
}

// Client 客户端连接
type Client struct {
	// WebSocket连接
	conn *websocket.Conn
	// 客户端唯一ID
	id string
	// 用户ID
	userID uint64
	// 设备ID
	deviceID string
	// 发送消息通道
	send chan []byte
	// 管理器引用
	manager *Manager
	// 最后活跃时间
	lastActive time.Time
	// 互斥锁
	mutex sync.RWMutex
}

// Message 消息结构
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
	MessageID string      `json:"message_id,omitempty"`
}

// UserMessage 发送给用户的消息
type UserMessage struct {
	UserID  uint64  `json:"user_id"`
	Message Message `json:"message"`
}

// ChatMessage 发送给会话的消息
type ChatMessage struct {
	ChatID  uint64  `json:"chat_id"`
	Message Message `json:"message"`
	// 排除的用户ID列表（比如发送者自己）
	ExcludeUsers []uint64 `json:"exclude_users,omitempty"`
}

// NewManager 创建新的管理器
func NewManager() *Manager {
	return &Manager{
		clients:         make(map[string]*Client),
		userConnections: make(map[uint64]map[string]*Client),
		chatRooms:       make(map[uint64]map[string]*Client),
		register:        make(chan *Client, 256),
		unregister:      make(chan *Client, 256),
		broadcast:       make(chan *Message, 256),
		sendToUser:      make(chan *UserMessage, 256),
		sendToChat:      make(chan *ChatMessage, 256),
		stopChan:        make(chan struct{}),
	}
}

// Run 运行管理器
func (m *Manager) Run() {
	// 启动心跳检测
	go m.heartbeatChecker()

	for {
		select {
		case client := <-m.register:
			m.registerClient(client)

		case client := <-m.unregister:
			m.unregisterClient(client)

		case message := <-m.broadcast:
			m.broadcastMessage(message)

		case userMsg := <-m.sendToUser:
			m.sendMessageToUser(userMsg)

		case chatMsg := <-m.sendToChat:
			m.sendMessageToChat(chatMsg)

		case <-m.stopChan:
			log.Println("WebSocket管理器正在停止...")
			return
		}
	}
}

// Stop 停止管理器
func (m *Manager) Stop() {
	close(m.stopChan)

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有连接
	for _, client := range m.clients {
		close(client.send)
		client.conn.Close()
	}
}

// RegisterClient 注册客户端
func (m *Manager) RegisterClient(client *Client) {
	m.register <- client
}

// UnregisterClient 注销客户端
func (m *Manager) UnregisterClient(client *Client) {
	m.unregister <- client
}

// BroadcastMessage 广播消息
func (m *Manager) BroadcastMessage(message *Message) {
	m.broadcast <- message
}

// SendToUser 发送消息给指定用户
func (m *Manager) SendToUser(userID uint64, message *Message) {
	m.sendToUser <- &UserMessage{
		UserID:  userID,
		Message: *message,
	}
}

// SendToChat 发送消息给指定会话
func (m *Manager) SendToChat(chatID uint64, message *Message, excludeUsers []uint64) {
	m.sendToChat <- &ChatMessage{
		ChatID:       chatID,
		Message:      *message,
		ExcludeUsers: excludeUsers,
	}
}

// registerClient 注册客户端
func (m *Manager) registerClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 添加到客户端列表
	m.clients[client.id] = client

	// 添加到用户连接列表
	if client.userID > 0 {
		if m.userConnections[client.userID] == nil {
			m.userConnections[client.userID] = make(map[string]*Client)
		}
		m.userConnections[client.userID][client.deviceID] = client
	}

	log.Printf("客户端已连接: %s, 用户ID: %d, 设备ID: %s", client.id, client.userID, client.deviceID)
}

// unregisterClient 注销客户端
func (m *Manager) unregisterClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 从客户端列表中删除
	if _, ok := m.clients[client.id]; ok {
		delete(m.clients, client.id)
		close(client.send)
	}

	// 从用户连接列表中删除
	if client.userID > 0 {
		if devices, ok := m.userConnections[client.userID]; ok {
			delete(devices, client.deviceID)
			if len(devices) == 0 {
				delete(m.userConnections, client.userID)
			}
		}
	}

	// 从所有聊天房间中移除客户端
	m.removeClientFromAllChats(client)

	log.Printf("客户端已断开: %s, 用户ID: %d, 设备ID: %s", client.id, client.userID, client.deviceID)
}

// broadcastMessage 广播消息
func (m *Manager) broadcastMessage(message *Message) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化广播消息失败: %v", err)
		return
	}

	for _, client := range m.clients {
		select {
		case client.send <- data:
		default:
			// 发送失败，关闭连接
			m.UnregisterClient(client)
		}
	}
}

// sendMessageToUser 发送消息给指定用户
func (m *Manager) sendMessageToUser(userMsg *UserMessage) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	data, err := json.Marshal(userMsg.Message)
	if err != nil {
		log.Printf("序列化用户消息失败: %v", err)
		return
	}

	// 发送给用户的所有设备
	if devices, ok := m.userConnections[userMsg.UserID]; ok {
		for _, client := range devices {
			select {
			case client.send <- data:
			default:
				// 发送失败，关闭连接
				m.UnregisterClient(client)
			}
		}
	}
}

// sendMessageToChat 发送消息给指定会话
func (m *Manager) sendMessageToChat(chatMsg *ChatMessage) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	data, err := json.Marshal(chatMsg.Message)
	if err != nil {
		log.Printf("序列化会话消息失败: %v", err)
		return
	}

	// 获取聊天房间中的客户端
	if room, ok := m.chatRooms[chatMsg.ChatID]; ok {
		excludeMap := make(map[uint64]bool)
		for _, userID := range chatMsg.ExcludeUsers {
			excludeMap[userID] = true
		}

		for _, client := range room {
			// 检查是否需要排除该用户
			if excludeMap[client.userID] {
				continue
			}

			select {
			case client.send <- data:
			default:
				// 发送失败，关闭连接
				m.UnregisterClient(client)
			}
		}
	}

	log.Printf("发送会话消息: 会话ID=%d, 房间成员数=%d", chatMsg.ChatID, len(m.chatRooms[chatMsg.ChatID]))
}

// heartbeatChecker 心跳检测
func (m *Manager) heartbeatChecker() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkHeartbeat()
		case <-m.stopChan:
			return
		}
	}
}

// checkHeartbeat 检查心跳
func (m *Manager) checkHeartbeat() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	now := time.Now()
	timeout := time.Duration(300) * time.Second // 5分钟超时

	for _, client := range m.clients {
		client.mutex.RLock()
		lastActive := client.lastActive
		client.mutex.RUnlock()

		if now.Sub(lastActive) > timeout {
			log.Printf("客户端心跳超时，断开连接: %s", client.id)
			m.UnregisterClient(client)
		}
	}
}

// GetOnlineUserCount 获取在线用户数量
func (m *Manager) GetOnlineUserCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.userConnections)
}

// GetTotalConnectionCount 获取总连接数
func (m *Manager) GetTotalConnectionCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.clients)
}

// JoinChat 将客户端加入聊天房间
func (m *Manager) JoinChat(client *Client, chatID uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.chatRooms[chatID] == nil {
		m.chatRooms[chatID] = make(map[string]*Client)
	}
	m.chatRooms[chatID][client.id] = client

	log.Printf("客户端 %s 加入聊天房间 %d", client.id, chatID)
}

// LeaveChat 将客户端从聊天房间移除
func (m *Manager) LeaveChat(client *Client, chatID uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if room, exists := m.chatRooms[chatID]; exists {
		delete(room, client.id)
		if len(room) == 0 {
			delete(m.chatRooms, chatID)
		}
	}

	log.Printf("客户端 %s 离开聊天房间 %d", client.id, chatID)
}

// BroadcastToChat 向聊天房间广播消息
func (m *Manager) BroadcastToChat(chatID uint64, message *WSMessage, excludeUserID uint64) {
	// 将WSMessage转换为Message
	msg := Message{
		Type:      string(message.Type),
		Data:      message.Data,
		Timestamp: message.Timestamp,
		MessageID: message.ID,
	}

	chatMsg := &ChatMessage{
		ChatID:       chatID,
		Message:      msg,
		ExcludeUsers: []uint64{},
	}

	if excludeUserID != 0 {
		chatMsg.ExcludeUsers = append(chatMsg.ExcludeUsers, excludeUserID)
	}

	select {
	case m.sendToChat <- chatMsg:
	default:
		log.Printf("聊天消息队列已满，丢弃消息")
	}
}

// removeClientFromAllChats 从所有聊天房间中移除客户端
func (m *Manager) removeClientFromAllChats(client *Client) {
	for chatID, room := range m.chatRooms {
		if _, exists := room[client.id]; exists {
			delete(room, client.id)
			if len(room) == 0 {
				delete(m.chatRooms, chatID)
			}
		}
	}
}

// GetChatRoomClients 获取聊天房间中的客户端列表
func (m *Manager) GetChatRoomClients(chatID uint64) []*Client {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	room, exists := m.chatRooms[chatID]
	if !exists {
		return nil
	}

	clients := make([]*Client, 0, len(room))
	for _, client := range room {
		clients = append(clients, client)
	}
	return clients
}

// IsUserOnline 检查用户是否在线
func (m *Manager) IsUserOnline(userID uint64) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	connections, exists := m.userConnections[userID]
	return exists && len(connections) > 0
}

// GetOnlineUsers 获取在线用户列表
func (m *Manager) GetOnlineUsers() []uint64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	users := make([]uint64, 0, len(m.userConnections))
	for userID := range m.userConnections {
		if userID != 0 {
			users = append(users, userID)
		}
	}
	return users
}

// SetMessageHandler 设置消息处理器
func (m *Manager) SetMessageHandler(handler *MessageHandler) {
	m.messageHandler = handler
}
