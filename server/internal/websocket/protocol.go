package websocket

import (
	"encoding/json"
	"time"
)

// MessageType WebSocket消息类型
type MessageType string

const (
	// 客户端发送的消息类型
	TypeCreateChat    MessageType = "create_chat"     // 创建聊天
	TypeSendMessage   MessageType = "send_message"    // 发送消息
	TypeJoinChat      MessageType = "join_chat"       // 加入聊天
	TypeLeaveChat     MessageType = "leave_chat"      // 离开聊天
	TypeGetChatList   MessageType = "get_chat_list"   // 获取聊天列表
	TypeGetMessages   MessageType = "get_messages"    // 获取消息列表
	TypeRecallMessage MessageType = "recall_message"  // 撤回消息
	TypeEditMessage   MessageType = "edit_message"    // 编辑消息
	TypeTyping        MessageType = "typing"          // 正在输入
	TypeHeartbeat     MessageType = "heartbeat"       // 心跳
	
	// 服务端发送的消息类型
	TypeChatCreated     MessageType = "chat_created"      // 聊天已创建
	TypeMessageReceived MessageType = "message_received"  // 收到新消息
	TypeMessageRecalled MessageType = "message_recalled"  // 消息已撤回
	TypeMessageEdited   MessageType = "message_edited"    // 消息已编辑
	TypeUserJoined      MessageType = "user_joined"       // 用户加入
	TypeUserLeft        MessageType = "user_left"         // 用户离开
	TypeUserTyping      MessageType = "user_typing"       // 用户正在输入
	TypeError           MessageType = "error"             // 错误消息
	TypeSuccess         MessageType = "success"           // 成功响应
)

// WSMessage WebSocket消息结构
type WSMessage struct {
	ID        string      `json:"id"`                  // 消息ID，用于请求响应匹配
	Type      MessageType `json:"type"`                // 消息类型
	Data      interface{} `json:"data,omitempty"`      // 消息数据
	Timestamp int64       `json:"timestamp"`           // 时间戳
	Error     *string     `json:"error,omitempty"`     // 错误信息
}

// NewWSMessage 创建WebSocket消息
func NewWSMessage(msgType MessageType, data interface{}) *WSMessage {
	return &WSMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewErrorMessage 创建错误消息
func NewErrorMessage(id string, err string) *WSMessage {
	return &WSMessage{
		ID:        id,
		Type:      TypeError,
		Error:     &err,
		Timestamp: time.Now().Unix(),
	}
}

// NewSuccessMessage 创建成功响应消息
func NewSuccessMessage(id string, data interface{}) *WSMessage {
	return &WSMessage{
		ID:        id,
		Type:      TypeSuccess,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// ToJSON 转换为JSON字符串
func (m *WSMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON 从JSON字符串解析
func FromJSON(data []byte) (*WSMessage, error) {
	var msg WSMessage
	err := json.Unmarshal(data, &msg)
	return &msg, err
}

// 创建聊天请求数据
type CreateChatData struct {
	Type        int      `json:"type"`                   // 聊天类型：1-私聊，2-群聊
	Name        *string  `json:"name,omitempty"`         // 聊天名称（群聊必填）
	Avatar      *string  `json:"avatar,omitempty"`       // 聊天头像
	Description *string  `json:"description,omitempty"`  // 聊天描述
	MemberIDs   []uint64 `json:"member_ids"`             // 成员ID列表
}

// 发送消息请求数据
type SendMessageData struct {
	ChatID       uint64      `json:"chat_id"`                    // 聊天ID
	Content      *string     `json:"content,omitempty"`          // 消息内容
	ContentType  int         `json:"content_type"`               // 内容类型：1-文本，2-图片，3-文件，4-语音，5-视频
	ReplyToMsgID *uint64     `json:"reply_to_msg_id,omitempty"`  // 回复的消息ID
	Extra        interface{} `json:"extra,omitempty"`            // 扩展数据
	ToUserID     *uint64     `json:"to_user_id,omitempty"`       // 私聊目标用户ID（用于懒创建）
}

// 加入聊天请求数据
type JoinChatData struct {
	ChatID uint64 `json:"chat_id"` // 聊天ID
}

// 离开聊天请求数据
type LeaveChatData struct {
	ChatID uint64 `json:"chat_id"` // 聊天ID
}

// 获取聊天列表请求数据
type GetChatListData struct {
	Page     int `json:"page"`      // 页码
	PageSize int `json:"page_size"` // 每页大小
}

// 获取消息列表请求数据
type GetMessagesData struct {
	ChatID   uint64 `json:"chat_id"`   // 聊天ID
	Page     int    `json:"page"`      // 页码
	PageSize int    `json:"page_size"` // 每页大小
}

// 撤回消息请求数据
type RecallMessageData struct {
	MessageID uint64 `json:"message_id"` // 消息ID
}

// 编辑消息请求数据
type EditMessageData struct {
	MessageID uint64      `json:"message_id"`        // 消息ID
	Content   *string     `json:"content,omitempty"` // 新内容
	Extra     interface{} `json:"extra,omitempty"`   // 新扩展数据
}

// 正在输入请求数据
type TypingData struct {
	ChatID uint64 `json:"chat_id"` // 聊天ID
}

// 聊天已创建响应数据
type ChatCreatedData struct {
	Chat    interface{} `json:"chat"`    // 聊天信息
	Members interface{} `json:"members"` // 成员列表
}

// 收到新消息响应数据
type MessageReceivedData struct {
	Message interface{} `json:"message"` // 消息信息
}

// 消息已撤回响应数据
type MessageRecalledData struct {
	MessageID uint64 `json:"message_id"` // 消息ID
	ChatID    uint64 `json:"chat_id"`    // 聊天ID
}

// 消息已编辑响应数据
type MessageEditedData struct {
	Message interface{} `json:"message"` // 更新后的消息信息
}

// 用户加入响应数据
type UserJoinedData struct {
	ChatID uint64      `json:"chat_id"` // 聊天ID
	User   interface{} `json:"user"`    // 用户信息
}

// 用户离开响应数据
type UserLeftData struct {
	ChatID uint64 `json:"chat_id"` // 聊天ID
	UserID uint64 `json:"user_id"` // 用户ID
}

// 用户正在输入响应数据
type UserTypingData struct {
	ChatID uint64      `json:"chat_id"` // 聊天ID
	User   interface{} `json:"user"`    // 用户信息
}
