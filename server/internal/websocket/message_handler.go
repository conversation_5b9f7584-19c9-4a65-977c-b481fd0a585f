package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"liyu/internal/domain/chat"
	"liyu/internal/domain/message"
)

// MessageHandler WebSocket消息处理器
type MessageHandler struct {
	chatService    chat.Service
	messageService message.Service
	manager        *Manager
}

// NewMessageHandler 创建消息处理器
func NewMessageHandler(chatService chat.Service, messageService message.Service, manager *Manager) *MessageHandler {
	return &MessageHandler{
		chatService:    chatService,
		messageService: messageService,
		manager:        manager,
	}
}

// HandleMessage 处理WebSocket消息
func (h *MessageHandler) HandleMessage(client *Client, msg *WSMessage) {
	ctx := context.Background()

	switch msg.Type {
	case TypeCreateChat:
		h.handleCreateChat(ctx, client, msg)
	case TypeSendMessage:
		h.handleSendMessage(ctx, client, msg)
	case TypeJoinChat:
		h.handleJoinChat(ctx, client, msg)
	case TypeLeaveChat:
		h.handleLeaveChat(ctx, client, msg)
	case TypeGetChatList:
		h.handleGetChatList(ctx, client, msg)
	case TypeGetMessages:
		h.handleGetMessages(ctx, client, msg)
	case TypeRecallMessage:
		h.handleRecallMessage(ctx, client, msg)
	case TypeEditMessage:
		h.handleEditMessage(ctx, client, msg)
	case TypeTyping:
		h.handleTyping(ctx, client, msg)
	case TypeHeartbeat:
		h.handleHeartbeat(ctx, client, msg)
	default:
		h.sendError(client, msg.ID, "未知的消息类型")
	}
}

// handleCreateChat 处理创建聊天
func (h *MessageHandler) handleCreateChat(ctx context.Context, client *Client, msg *WSMessage) {
	var data CreateChatData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 创建聊天请求
	req := &chat.CreateChatRequest{
		Type:        chat.Type(data.Type),
		Name:        data.Name,
		Avatar:      data.Avatar,
		Description: data.Description,
		MemberIDs:   data.MemberIDs,
	}

	// 创建聊天
	newChat, err := h.chatService.CreateGroupChat(ctx, req, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("创建聊天失败: %v", err))
		return
	}

	// 获取聊天成员
	members, err := h.chatService.GetChatMembers(ctx, newChat.ID, client.userID)
	if err != nil {
		log.Printf("获取聊天成员失败: %v", err)
		members = []*chat.Member{} // 使用空列表
	}

	// 响应创建者
	responseData := ChatCreatedData{
		Chat:    newChat,
		Members: members,
	}
	h.sendSuccess(client, msg.ID, responseData)

	// 通知所有成员聊天已创建
	h.broadcastToChatMembers(newChat.ID, TypeChatCreated, responseData, client.userID)
}

// handleSendMessage 处理发送消息
func (h *MessageHandler) handleSendMessage(ctx context.Context, client *Client, msg *WSMessage) {
	var data SendMessageData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	var chatID uint64
	var err error

	// 如果指定了聊天ID，直接使用
	if data.ChatID != 0 {
		chatID = data.ChatID

		// 检查用户是否为聊天成员
		isMember, err := h.chatService.IsMember(ctx, chatID, client.userID)
		if err != nil {
			h.sendError(client, msg.ID, "检查聊天成员失败")
			return
		}
		if !isMember {
			h.sendError(client, msg.ID, "您不是该聊天的成员")
			return
		}
	} else if data.ToUserID != nil {
		// 私聊懒创建：获取或创建私聊
		privateChat, err := h.chatService.GetOrCreatePrivateChat(ctx, client.userID, *data.ToUserID)
		if err != nil {
			h.sendError(client, msg.ID, fmt.Sprintf("创建私聊失败: %v", err))
			return
		}
		chatID = privateChat.ID
	} else {
		h.sendError(client, msg.ID, "必须指定聊天ID或目标用户ID")
		return
	}

	// 创建发送消息请求
	req := &message.SendMessageRequest{
		ChatID:       chatID,
		Content:      data.Content,
		ContentType:  message.ContentType(data.ContentType),
		ReplyToMsgID: data.ReplyToMsgID,
	}

	// 处理Extra字段的类型转换
	if data.Extra != nil {
		if extraStr, ok := data.Extra.(string); ok {
			req.Extra = &extraStr
		} else if extraBytes, err := json.Marshal(data.Extra); err == nil {
			extraStr := string(extraBytes)
			req.Extra = &extraStr
		}
	}

	// 发送消息
	newMessage, err := h.messageService.SendMessage(ctx, req, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("发送消息失败: %v", err))
		return
	}

	// 更新聊天的最后消息信息
	err = h.chatService.UpdateLastMessage(ctx, chatID, newMessage.ID, newMessage.Seq)
	if err != nil {
		log.Printf("更新聊天最后消息失败: %v", err)
	}

	// 响应发送者
	h.sendSuccess(client, msg.ID, newMessage)

	// 广播消息给聊天中的所有成员
	responseData := MessageReceivedData{Message: newMessage}
	h.broadcastToChatMembers(chatID, TypeMessageReceived, responseData, 0) // 0表示广播给所有人
}

// handleJoinChat 处理加入聊天
func (h *MessageHandler) handleJoinChat(ctx context.Context, client *Client, msg *WSMessage) {
	var data JoinChatData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 检查用户是否已经是聊天成员
	isMember, err := h.chatService.IsMember(ctx, data.ChatID, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, "检查聊天成员失败")
		return
	}
	if !isMember {
		h.sendError(client, msg.ID, "您不是该聊天的成员")
		return
	}

	// 将客户端加入聊天房间
	h.manager.JoinChat(client, data.ChatID)

	h.sendSuccess(client, msg.ID, map[string]interface{}{
		"chat_id": data.ChatID,
		"message": "成功加入聊天",
	})
}

// handleLeaveChat 处理离开聊天
func (h *MessageHandler) handleLeaveChat(ctx context.Context, client *Client, msg *WSMessage) {
	var data LeaveChatData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 将客户端从聊天房间移除
	h.manager.LeaveChat(client, data.ChatID)

	h.sendSuccess(client, msg.ID, map[string]interface{}{
		"chat_id": data.ChatID,
		"message": "成功离开聊天",
	})
}

// handleGetChatList 处理获取聊天列表
func (h *MessageHandler) handleGetChatList(ctx context.Context, client *Client, msg *WSMessage) {
	var data GetChatListData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	chats, total, err := h.chatService.GetUserChats(ctx, client.userID, data.Page, data.PageSize)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("获取聊天列表失败: %v", err))
		return
	}

	responseData := map[string]interface{}{
		"chats": chats,
		"total": total,
		"page":  data.Page,
	}

	h.sendSuccess(client, msg.ID, responseData)
}

// handleGetMessages 处理获取消息列表
func (h *MessageHandler) handleGetMessages(ctx context.Context, client *Client, msg *WSMessage) {
	var data GetMessagesData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 检查用户是否为聊天成员
	isMember, err := h.chatService.IsMember(ctx, data.ChatID, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, "检查聊天成员失败")
		return
	}
	if !isMember {
		h.sendError(client, msg.ID, "您不是该聊天的成员")
		return
	}

	messages, total, err := h.messageService.GetChatMessages(ctx, data.ChatID, client.userID, data.Page, data.PageSize)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("获取消息列表失败: %v", err))
		return
	}

	responseData := map[string]interface{}{
		"messages": messages,
		"total":    total,
		"page":     data.Page,
	}

	h.sendSuccess(client, msg.ID, responseData)
}

// handleRecallMessage 处理撤回消息
func (h *MessageHandler) handleRecallMessage(ctx context.Context, client *Client, msg *WSMessage) {
	var data RecallMessageData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 获取消息信息以获取聊天ID
	msgInfo, err := h.messageService.GetMessage(ctx, data.MessageID, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, "消息不存在")
		return
	}

	// 撤回消息
	err = h.messageService.RecallMessage(ctx, data.MessageID, client.userID)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("撤回消息失败: %v", err))
		return
	}

	// 响应操作者
	h.sendSuccess(client, msg.ID, map[string]interface{}{
		"message_id": data.MessageID,
		"message":    "消息已撤回",
	})

	// 广播撤回通知给聊天中的所有成员
	responseData := MessageRecalledData{
		MessageID: data.MessageID,
		ChatID:    msgInfo.ChatID,
	}
	h.broadcastToChatMembers(msgInfo.ChatID, TypeMessageRecalled, responseData, 0)
}

// handleEditMessage 处理编辑消息
func (h *MessageHandler) handleEditMessage(ctx context.Context, client *Client, msg *WSMessage) {
	var data EditMessageData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 编辑消息请求
	req := &message.UpdateMessageRequest{
		Content: data.Content,
	}

	// 处理Extra字段的类型转换
	if data.Extra != nil {
		if extraStr, ok := data.Extra.(string); ok {
			req.Extra = &extraStr
		} else if extraBytes, err := json.Marshal(data.Extra); err == nil {
			extraStr := string(extraBytes)
			req.Extra = &extraStr
		}
	}

	// 编辑消息
	updatedMessage, err := h.messageService.UpdateMessage(ctx, data.MessageID, client.userID, req)
	if err != nil {
		h.sendError(client, msg.ID, fmt.Sprintf("编辑消息失败: %v", err))
		return
	}

	// 响应操作者
	h.sendSuccess(client, msg.ID, updatedMessage)

	// 广播编辑通知给聊天中的所有成员
	responseData := MessageEditedData{Message: updatedMessage}
	h.broadcastToChatMembers(updatedMessage.ChatID, TypeMessageEdited, responseData, 0)
}

// handleTyping 处理正在输入
func (h *MessageHandler) handleTyping(ctx context.Context, client *Client, msg *WSMessage) {
	var data TypingData
	if err := h.parseMessageData(msg.Data, &data); err != nil {
		h.sendError(client, msg.ID, "无效的请求数据")
		return
	}

	// 检查用户是否为聊天成员
	isMember, err := h.chatService.IsMember(ctx, data.ChatID, client.userID)
	if err != nil || !isMember {
		return // 静默忽略
	}

	// 广播正在输入状态给聊天中的其他成员
	responseData := UserTypingData{
		ChatID: data.ChatID,
		User: map[string]interface{}{
			"user_id": client.userID,
		},
	}
	h.broadcastToChatMembers(data.ChatID, TypeUserTyping, responseData, client.userID)
}

// handleHeartbeat 处理心跳
func (h *MessageHandler) handleHeartbeat(ctx context.Context, client *Client, msg *WSMessage) {
	// 更新客户端最后活跃时间
	client.UpdateLastSeen()

	// 响应心跳
	h.sendSuccess(client, msg.ID, map[string]interface{}{
		"timestamp": msg.Timestamp,
	})
}

// 辅助方法

// parseMessageData 解析消息数据
func (h *MessageHandler) parseMessageData(data interface{}, target interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, target)
}

// sendError 发送错误消息
func (h *MessageHandler) sendError(client *Client, msgID string, errMsg string) {
	errorMsg := NewErrorMessage(msgID, errMsg)
	client.Send(errorMsg)
}

// sendSuccess 发送成功响应
func (h *MessageHandler) sendSuccess(client *Client, msgID string, data interface{}) {
	successMsg := NewSuccessMessage(msgID, data)
	client.Send(successMsg)
}

// broadcastToChatMembers 向聊天成员广播消息
func (h *MessageHandler) broadcastToChatMembers(chatID uint64, msgType MessageType, data interface{}, excludeUserID uint64) {
	broadcastMsg := NewWSMessage(msgType, data)
	h.manager.BroadcastToChat(chatID, broadcastMsg, excludeUserID)
}
