package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"

	"liyu/internal/auth"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 在生产环境中应该检查Origin
		return true
	},
}

// HandleWebSocket 处理WebSocket连接
func HandleWebSocket(c *gin.Context, manager *Manager) {
	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 创建客户端
	client := &Client{
		conn:       conn,
		id:         uuid.New().String(),
		send:       make(chan []byte, 256),
		manager:    manager,
		lastActive: time.Now(),
	}

	// 注册客户端
	manager.RegisterClient(client)

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// readPump 读取消息
func (c *Client) readPump() {
	defer func() {
		c.manager.UnregisterClient(c)
		c.conn.Close()
	}()

	// 设置读取超时
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		c.updateLastActive()
		return nil
	})

	for {
		_, messageBytes, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}

		c.updateLastActive()
		c.handleMessage(messageBytes)
	}
}

// writePump 发送消息
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(messageBytes []byte) {
	// 首先尝试解析为WSMessage格式
	wsMsg, err := FromJSON(messageBytes)
	if err == nil && c.manager.messageHandler != nil {
		// 使用新的消息处理器
		c.manager.messageHandler.HandleMessage(c, wsMsg)
		return
	}

	// 兼容旧的Message格式
	var msg Message
	if err := json.Unmarshal(messageBytes, &msg); err != nil {
		log.Printf("解析消息失败: %v", err)
		c.sendError("invalid_message", "消息格式错误")
		return
	}

	switch msg.Type {
	case "auth":
		c.handleAuth(msg.Data)
	case "heartbeat":
		c.handleHeartbeat(msg.Data)
	case "send_message":
		c.handleSendMessage(msg.Data)
	default:
		c.sendError("unknown_message_type", "未知的消息类型")
	}
}

// handleAuth 处理认证
func (c *Client) handleAuth(data interface{}) {
	authData, ok := data.(map[string]interface{})
	if !ok {
		c.sendError("invalid_auth_data", "认证数据格式错误")
		return
	}

	token, ok := authData["token"].(string)
	if !ok {
		c.sendError("missing_token", "缺少认证令牌")
		return
	}

	deviceID, ok := authData["device_id"].(string)
	if !ok {
		c.sendError("missing_device_id", "缺少设备ID")
		return
	}

	// 验证JWT令牌
	claims, err := auth.ValidateToken(token)
	if err != nil {
		c.sendError("invalid_token", "无效的认证令牌")
		return
	}

	// 更新客户端信息
	c.mutex.Lock()
	c.userID = claims.UserID
	c.deviceID = deviceID
	c.mutex.Unlock()

	// 重新注册客户端（更新用户信息）
	c.manager.RegisterClient(c)

	// 发送认证成功消息
	c.sendMessage("auth_success", map[string]interface{}{
		"user_id":   c.userID,
		"device_id": c.deviceID,
	})

	log.Printf("用户认证成功: 用户ID=%d, 设备ID=%s, 客户端ID=%s", c.userID, c.deviceID, c.id)
}

// handleHeartbeat 处理心跳
func (c *Client) handleHeartbeat(data interface{}) {
	c.updateLastActive()

	// 回复心跳
	c.sendMessage("heartbeat_response", map[string]interface{}{
		"timestamp": time.Now().Unix(),
	})
}

// handleSendMessage 处理发送消息
func (c *Client) handleSendMessage(data interface{}) {
	if c.userID == 0 {
		c.sendError("not_authenticated", "用户未认证")
		return
	}

	// TODO: 实现发送消息逻辑
	log.Printf("用户 %d 发送消息: %v", c.userID, data)
}

// sendMessage 发送消息给客户端
func (c *Client) sendMessage(msgType string, data interface{}) {
	message := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
		MessageID: uuid.New().String(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化消息失败: %v", err)
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		// 发送队列已满，关闭连接
		c.manager.UnregisterClient(c)
	}
}

// sendError 发送错误消息
func (c *Client) sendError(code, message string) {
	c.sendMessage("error", map[string]interface{}{
		"code":    code,
		"message": message,
	})
}

// updateLastActive 更新最后活跃时间
func (c *Client) updateLastActive() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.lastActive = time.Now()
}

// Send 发送WSMessage格式的消息
func (c *Client) Send(wsMsg *WSMessage) {
	messageBytes, err := wsMsg.ToJSON()
	if err != nil {
		log.Printf("序列化WSMessage失败: %v", err)
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		// 发送队列已满，关闭连接
		c.manager.UnregisterClient(c)
	}
}

// UpdateLastSeen 更新最后活跃时间（兼容MessageHandler接口）
func (c *Client) UpdateLastSeen() {
	c.updateLastActive()
}
