package websocket

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"liyu/internal/auth"
	"liyu/internal/domain/chat"
	"liyu/internal/domain/message"
)

// WebSocketService WebSocket服务
type WebSocketService struct {
	manager        *Manager
	messageHandler *MessageHandler
	chatService    chat.Service
	messageService message.Service
}

// NewWebSocketService 创建WebSocket服务
func NewWebSocketService(db *gorm.DB) *WebSocketService {
	// 创建仓储
	chatRepo := chat.NewGormRepository(db)
	messageRepo := message.NewGormRepository(db)

	// 创建服务
	chatService := chat.NewService(chatRepo, messageRepo, db)
	messageService := message.NewService(messageRepo, db)

	// 创建管理器
	manager := NewManager()

	// 创建消息处理器
	messageHandler := NewMessageHandler(chatService, messageService, manager)

	// 设置消息处理器
	manager.SetMessageHandler(messageHandler)

	service := &WebSocketService{
		manager:        manager,
		messageHandler: messageHandler,
		chatService:    chatService,
		messageService: messageService,
	}

	// 启动管理器
	go manager.Run()

	return service
}

// HandleWebSocketConnection 处理WebSocket连接
func (s *WebSocketService) HandleWebSocketConnection(c *gin.Context) {
	// 从查询参数或Header中获取认证信息
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("Authorization")
		if token != "" && len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}
	}

	var userID uint64
	var deviceID string

	// 验证token并获取用户信息
	if token != "" {
		claims, err := auth.ValidateToken(token)
		if err != nil {
			log.Printf("Token验证失败: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的token"})
			return
		}
		userID = claims.UserID
		deviceID = c.Query("device_id")
		if deviceID == "" {
			deviceID = "default"
		}
	}

	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	// 创建客户端
	client := &Client{
		conn:       conn,
		id:         uuid.New().String(),
		userID:     userID,
		deviceID:   deviceID,
		send:       make(chan []byte, 256),
		manager:    s.manager,
		lastActive: time.Now(),
	}

	// 注册客户端
	s.manager.RegisterClient(client)

	// 启动读写协程
	go client.writePump()
	go client.readPump()

	log.Printf("WebSocket连接已建立: ClientID=%s, UserID=%d, DeviceID=%s",
		client.id, client.userID, client.deviceID)
}

// GetManager 获取WebSocket管理器
func (s *WebSocketService) GetManager() *Manager {
	return s.manager
}

// GetChatService 获取聊天服务
func (s *WebSocketService) GetChatService() chat.Service {
	return s.chatService
}

// GetMessageService 获取消息服务
func (s *WebSocketService) GetMessageService() message.Service {
	return s.messageService
}

// BroadcastToChat 向聊天广播消息
func (s *WebSocketService) BroadcastToChat(chatID uint64, msgType MessageType, data interface{}, excludeUserID uint64) {
	message := NewWSMessage(msgType, data)
	s.manager.BroadcastToChat(chatID, message, excludeUserID)
}

// SendToUser 向用户发送消息
func (s *WebSocketService) SendToUser(userID uint64, msgType MessageType, data interface{}) bool {
	wsMessage := NewWSMessage(msgType, data)

	// 将WSMessage转换为Message
	message := &Message{
		Type:      string(wsMessage.Type),
		Data:      wsMessage.Data,
		Timestamp: wsMessage.Timestamp,
		MessageID: wsMessage.ID,
	}

	s.manager.SendToUser(userID, message)
	return true // 简化返回值，实际应该检查发送结果
}

// NotifyChatCreated 通知聊天已创建
func (s *WebSocketService) NotifyChatCreated(chat *chat.Chat, members []*chat.Member) {
	data := ChatCreatedData{
		Chat:    chat,
		Members: members,
	}

	// 通知所有成员
	for _, member := range members {
		s.SendToUser(member.UserID, TypeChatCreated, data)
	}
}

// NotifyMessageReceived 通知收到新消息
func (s *WebSocketService) NotifyMessageReceived(msg *message.Message, chatID uint64) {
	data := MessageReceivedData{
		Message: msg,
	}

	// 广播给聊天中的所有成员
	s.BroadcastToChat(chatID, TypeMessageReceived, data, 0)
}

// NotifyMessageRecalled 通知消息已撤回
func (s *WebSocketService) NotifyMessageRecalled(messageID, chatID uint64) {
	data := MessageRecalledData{
		MessageID: messageID,
		ChatID:    chatID,
	}

	// 广播给聊天中的所有成员
	s.BroadcastToChat(chatID, TypeMessageRecalled, data, 0)
}

// NotifyMessageEdited 通知消息已编辑
func (s *WebSocketService) NotifyMessageEdited(msg *message.Message) {
	data := MessageEditedData{
		Message: msg,
	}

	// 广播给聊天中的所有成员
	s.BroadcastToChat(msg.ChatID, TypeMessageEdited, data, 0)
}

// NotifyUserJoined 通知用户加入聊天
func (s *WebSocketService) NotifyUserJoined(chatID, userID uint64, user interface{}) {
	data := UserJoinedData{
		ChatID: chatID,
		User:   user,
	}

	// 广播给聊天中的所有成员
	s.BroadcastToChat(chatID, TypeUserJoined, data, 0)
}

// NotifyUserLeft 通知用户离开聊天
func (s *WebSocketService) NotifyUserLeft(chatID, userID uint64) {
	data := UserLeftData{
		ChatID: chatID,
		UserID: userID,
	}

	// 广播给聊天中的所有成员
	s.BroadcastToChat(chatID, TypeUserLeft, data, userID)
}

// GetOnlineUsers 获取在线用户列表
func (s *WebSocketService) GetOnlineUsers() []uint64 {
	return s.manager.GetOnlineUsers()
}

// IsUserOnline 检查用户是否在线
func (s *WebSocketService) IsUserOnline(userID uint64) bool {
	return s.manager.IsUserOnline(userID)
}

// GetStats 获取WebSocket统计信息
func (s *WebSocketService) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_connections": s.manager.GetTotalConnectionCount(),
		"online_users":      len(s.manager.GetOnlineUsers()),
		"active_chats":      len(s.manager.chatRooms),
	}
}

// Stop 停止WebSocket服务
func (s *WebSocketService) Stop() {
	s.manager.Stop()
}
