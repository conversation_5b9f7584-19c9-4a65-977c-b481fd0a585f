package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server      Server      `yaml:"server"`
	Database    Database    `yaml:"database"`
	Log         Log         `yaml:"log"`
	JWT         JWT         `yaml:"jwt"`
	Redis       Redis       `yaml:"redis"`
	Upload      Upload      `yaml:"upload"`
	Message     Message     `yaml:"message"`
	Development Development `yaml:"development"`
}

// Server 服务器配置
type Server struct {
	HTTP      HTTP      `yaml:"http"`
	WebSocket WebSocket `yaml:"websocket"`
}

// HTTP HTTP服务器配置
type HTTP struct {
	Host      string `yaml:"host"`
	Port      int    `yaml:"port"`
	EnableSSL bool   `yaml:"enable_ssl"`
	SSL       SSL    `yaml:"ssl"`
}

// WebSocket WebSocket服务器配置
type WebSocket struct {
	Host               string `yaml:"host"`
	Port               int    `yaml:"port"`
	EnableSSL          bool   `yaml:"enable_ssl"`
	SSL                SSL    `yaml:"ssl"`
	ReadBufferSize     int    `yaml:"read_buffer_size"`
	WriteBufferSize    int    `yaml:"write_buffer_size"`
	HeartbeatInterval  int    `yaml:"heartbeat_interval"`
	ConnectionTimeout  int    `yaml:"connection_timeout"`
}

// SSL SSL配置
type SSL struct {
	CertFile string `yaml:"cert_file"`
	KeyFile  string `yaml:"key_file"`
}

// Database 数据库配置
type Database struct {
	MySQL MySQL `yaml:"mysql"`
}

// MySQL MySQL配置
type MySQL struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	Username        string `yaml:"username"`
	Password        string `yaml:"password"`
	Database        string `yaml:"database"`
	Charset         string `yaml:"charset"`
	MaxIdleConns    int    `yaml:"max_idle_conns"`
	MaxOpenConns    int    `yaml:"max_open_conns"`
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"`
	LogLevel        string `yaml:"log_level"`
}

// Log 日志配置
type Log struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// JWT JWT配置
type JWT struct {
	Secret              string `yaml:"secret"`
	ExpiresHours        int    `yaml:"expires_hours"`
	RefreshExpiresHours int    `yaml:"refresh_expires_hours"`
}

// Redis Redis配置
type Redis struct {
	Enabled      bool   `yaml:"enabled"`
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	Password     string `yaml:"password"`
	Database     int    `yaml:"database"`
	PoolSize     int    `yaml:"pool_size"`
	MinIdleConns int    `yaml:"min_idle_conns"`
}

// Upload 文件上传配置
type Upload struct {
	Path         string   `yaml:"path"`
	MaxSize      int      `yaml:"max_size"`
	AllowedTypes []string `yaml:"allowed_types"`
}

// Message 消息配置
type Message struct {
	MaxPullCount      int  `yaml:"max_pull_count"`
	MaxContentLength  int  `yaml:"max_content_length"`
	EnableEncryption  bool `yaml:"enable_encryption"`
	RecallTimeLimit   int  `yaml:"recall_time_limit"` // 撤回时间限制（分钟）
}

// Development 开发模式配置
type Development struct {
	Enabled       bool `yaml:"enabled"`
	EnableSwagger bool `yaml:"enable_swagger"`
	EnableCORS    bool `yaml:"enable_cors"`
}

var AppConfig *Config

// Load 加载配置文件
func Load(configPath string) error {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	config := &Config{}
	if err := yaml.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 设置默认值
	config.setDefaults()

	AppConfig = config
	return nil
}

// setDefaults 设置默认值
func (c *Config) setDefaults() {
	// 服务器默认配置
	if c.Server.HTTP.Host == "" {
		c.Server.HTTP.Host = "0.0.0.0"
	}
	if c.Server.HTTP.Port == 0 {
		c.Server.HTTP.Port = 8080
	}
	if c.Server.WebSocket.Host == "" {
		c.Server.WebSocket.Host = "0.0.0.0"
	}
	if c.Server.WebSocket.Port == 0 {
		c.Server.WebSocket.Port = 8081
	}
	if c.Server.WebSocket.ReadBufferSize == 0 {
		c.Server.WebSocket.ReadBufferSize = 1024
	}
	if c.Server.WebSocket.WriteBufferSize == 0 {
		c.Server.WebSocket.WriteBufferSize = 1024
	}
	if c.Server.WebSocket.HeartbeatInterval == 0 {
		c.Server.WebSocket.HeartbeatInterval = 30
	}
	if c.Server.WebSocket.ConnectionTimeout == 0 {
		c.Server.WebSocket.ConnectionTimeout = 300
	}

	// 数据库默认配置
	if c.Database.MySQL.Charset == "" {
		c.Database.MySQL.Charset = "utf8mb4"
	}
	if c.Database.MySQL.MaxIdleConns == 0 {
		c.Database.MySQL.MaxIdleConns = 10
	}
	if c.Database.MySQL.MaxOpenConns == 0 {
		c.Database.MySQL.MaxOpenConns = 100
	}
	if c.Database.MySQL.ConnMaxLifetime == 0 {
		c.Database.MySQL.ConnMaxLifetime = 3600
	}

	// JWT默认配置
	if c.JWT.ExpiresHours == 0 {
		c.JWT.ExpiresHours = 24
	}
	if c.JWT.RefreshExpiresHours == 0 {
		c.JWT.RefreshExpiresHours = 168
	}

	// 消息默认配置
	if c.Message.MaxPullCount == 0 {
		c.Message.MaxPullCount = 100
	}
	if c.Message.MaxContentLength == 0 {
		c.Message.MaxContentLength = 10000
	}
	if c.Message.RecallTimeLimit == 0 {
		c.Message.RecallTimeLimit = 2 // 2分钟
	}

	// 上传默认配置
	if c.Upload.Path == "" {
		c.Upload.Path = "./uploads"
	}
	if c.Upload.MaxSize == 0 {
		c.Upload.MaxSize = 100
	}
}

// GetHTTPAddr 获取HTTP服务器地址
func (c *Config) GetHTTPAddr() string {
	return fmt.Sprintf("%s:%d", c.Server.HTTP.Host, c.Server.HTTP.Port)
}

// GetWebSocketAddr 获取WebSocket服务器地址
func (c *Config) GetWebSocketAddr() string {
	return fmt.Sprintf("%s:%d", c.Server.WebSocket.Host, c.Server.WebSocket.Port)
}

// GetMySQLDSN 获取MySQL连接字符串
func (c *Config) GetMySQLDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.Database.MySQL.Username,
		c.Database.MySQL.Password,
		c.Database.MySQL.Host,
		c.Database.MySQL.Port,
		c.Database.MySQL.Database,
		c.Database.MySQL.Charset,
	)
}

// GetRedisAddr 获取Redis地址
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}
