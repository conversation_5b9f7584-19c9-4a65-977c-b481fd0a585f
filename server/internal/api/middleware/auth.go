package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"liyu/internal/auth"
	"liyu/internal/common/response"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Error(c, http.StatusUnauthorized, "MISSING_AUTHORIZATION", "缺少认证头")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.Error(c, http.StatusUnauthorized, "INVALID_AUTHORIZATION", "无效的认证头格式")
			c.Abort()
			return
		}

		// 提取令牌
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			response.Error(c, http.StatusUnauthorized, "MISSING_TOKEN", "缺少认证令牌")
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := auth.ValidateToken(token)
		if err != nil {
			response.Error(c, http.StatusUnauthorized, "INVALID_TOKEN", "无效的认证令牌")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("account", claims.Account)
		c.Set("claims", claims)

		c.Next()
	}
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) uint64 {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint64); ok {
			return id
		}
	}
	return 0
}

// GetAccount 从上下文中获取用户账号
func GetAccount(c *gin.Context) string {
	if account, exists := c.Get("account"); exists {
		if acc, ok := account.(string); ok {
			return acc
		}
	}
	return ""
}

// GetClaims 从上下文中获取JWT声明
func GetClaims(c *gin.Context) *auth.Claims {
	if claims, exists := c.Get("claims"); exists {
		if c, ok := claims.(*auth.Claims); ok {
			return c
		}
	}
	return nil
}

// RequireOwnership 要求资源所有权中间件
func RequireOwnership() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := GetUserID(c)
		if userID == 0 {
			response.Error(c, http.StatusUnauthorized, "NOT_AUTHENTICATED", "用户未认证")
			c.Abort()
			return
		}

		// 从路径参数获取资源ID
		resourceID := c.Param("id")
		if resourceID == "" {
			response.Error(c, http.StatusBadRequest, "MISSING_RESOURCE_ID", "缺少资源ID")
			c.Abort()
			return
		}

		// 这里可以添加更复杂的所有权检查逻辑
		// 例如检查用户是否拥有指定的资源

		c.Next()
	}
}
