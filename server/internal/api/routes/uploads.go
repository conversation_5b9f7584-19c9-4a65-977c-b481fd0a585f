package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
	"liyu/internal/api/middleware"
)

// SetupUploadRoutes 设置文件上传相关路由
func SetupUploadRoutes(router *gin.RouterGroup, uploadHandler *handlers.UploadHandler) {
	uploads := router.Group("/uploads")
	uploads.Use(middleware.AuthMiddleware()) // 所有上传路由都需要认证
	{
		// POST /api/v1/uploads/images - 上传图片
		uploads.POST("/images", uploadHandler.UploadImage)

		// POST /api/v1/uploads/files - 上传文件
		uploads.POST("/files", uploadHandler.UploadFile)

		// POST /api/v1/uploads/audio - 上传音频
		uploads.POST("/audio", uploadHandler.UploadAudio)

		// POST /api/v1/uploads/video - 上传视频
		uploads.POST("/video", uploadHandler.UploadVideo)

		// GET /api/v1/uploads/:file_id - 获取文件信息
		uploads.GET("/:file_id", uploadHandler.GetFileInfo)

		// DELETE /api/v1/uploads/:file_id - 删除文件
		uploads.DELETE("/:file_id", uploadHandler.DeleteFile)
	}
}
