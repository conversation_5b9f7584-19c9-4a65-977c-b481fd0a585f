package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
	"liyu/internal/api/middleware"
)

// SetupUserRoutes 设置用户相关路由
func SetupUserRoutes(router *gin.RouterGroup, userHandler *handlers.UserHandler) {
	users := router.Group("/users")
	users.Use(middleware.AuthMiddleware()) // 所有用户路由都需要认证
	{
		// GET /api/v1/users - 获取用户列表（分页）
		users.GET("", userHandler.ListUsers)

		// GET /api/v1/users/:id - 获取指定用户信息
		users.GET("/:id", userHandler.GetUser)

		// PUT /api/v1/users/:id - 更新指定用户信息（只能更新自己）
		users.PUT("/:id", userHandler.UpdateUser)

		// DELETE /api/v1/users/:id - 删除指定用户（软删除，只能删除自己）
		users.DELETE("/:id", userHandler.DeleteUser)

		// POST /api/v1/users/:id/avatar - 上传用户头像
		users.POST("/:id/avatar", userHandler.UploadAvatar)

		// PUT /api/v1/users/:id/password - 修改用户密码
		users.PUT("/:id/password", userHandler.ChangePassword)

		// GET /api/v1/users/search - 搜索用户
		users.GET("/search", userHandler.SearchUsers)
	}
}
