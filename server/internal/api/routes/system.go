package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
)

// SetupSystemRoutes 设置系统相关路由
func SetupSystemRoutes(router *gin.RouterGroup, systemHandler *handlers.SystemHandler) {
	// 健康检查和系统状态不需要认证

	// GET /api/v1/health - 健康检查
	router.GET("/health", systemHandler.Health)

	// GET /api/v1/status - 系统状态
	router.GET("/status", systemHandler.GetStatus)

	// GET /api/v1/version - 版本信息
	router.GET("/version", systemHandler.GetVersion)

	// GET /api/v1/metrics - 系统指标（可选，用于监控）
	router.GET("/metrics", systemHandler.GetMetrics)
}
