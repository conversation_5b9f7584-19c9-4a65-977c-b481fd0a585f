package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
	"liyu/internal/api/middleware"
	"liyu/internal/websocket"
)

// SetupRoutes 设置所有路由
func SetupRoutes(
	router *gin.Engine,
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	chatHandler *handlers.ChatHandler,
	uploadHandler *handlers.UploadHandler,
	systemHandler *handlers.SystemHandler,
	wsManager *websocket.Manager,
) {
	// 添加全局中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// 管理页面路由（预留）
	router.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "管理页面功能待实现",
			"service": "liyu-admin",
			"version": "1.0.0",
		})
	})

	// API路由组
	api := router.Group("/api")
	{
		// API版本分组
		v1 := api.Group("/v1")
		{
			// 设置各模块路由
			SetupSystemRoutes(v1, systemHandler)
			SetupAuthRoutes(v1, authHandler)
			SetupUserRoutes(v1, userHandler)
			SetupChatRoutes(v1, chatHandler)
			SetupUploadRoutes(v1, uploadHandler)
		}

		// WebSocket路由
		api.GET("/ws", func(c *gin.Context) {
			websocket.HandleWebSocket(c, wsManager)
		})
	}

	// 静态文件服务
	router.Static("/static", "./static")
	router.Static("/uploads", "./uploads")

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		handlers.NotFound(c)
	})
}

// SetupWebSocketRoutes 设置WebSocket路由（用于独立的WebSocket服务器）
func SetupWebSocketRoutes(router *gin.Engine, wsManager *websocket.Manager) {
	// 添加基础中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// API路由组
	api := router.Group("/api")
	{
		// WebSocket连接
		api.GET("/ws", func(c *gin.Context) {
			websocket.HandleWebSocket(c, wsManager)
		})

		// WebSocket健康检查
		api.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":  "ok",
				"service": "websocket",
			})
		})
	}

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		handlers.NotFound(c)
	})
}

// SetupWebSocketRoutesWithService 设置WebSocket路由（使用WebSocket服务）
func SetupWebSocketRoutesWithService(router *gin.Engine, wsService *websocket.WebSocketService) {
	// 添加基础中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())

	// API路由组
	api := router.Group("/api")
	{
		// WebSocket连接
		api.GET("/ws", wsService.HandleWebSocketConnection)

		// WebSocket健康检查
		api.GET("/health", func(c *gin.Context) {
			stats := wsService.GetStats()
			c.JSON(200, gin.H{
				"status":  "ok",
				"service": "websocket",
				"stats":   stats,
			})
		})
	}

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		handlers.NotFound(c)
	})
}
