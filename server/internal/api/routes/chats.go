package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
	"liyu/internal/api/middleware"
)

// SetupChatRoutes 设置聊天相关路由
func SetupChatRoutes(router *gin.RouterGroup, chatHandler *handlers.ChatHandler) {
	chats := router.Group("/chats")
	chats.Use(middleware.AuthMiddleware()) // 所有聊天路由都需要认证
	{
		// GET /api/v1/chats - 获取当前用户的聊天列表
		chats.GET("", chatHandler.ListUserChats)

		// POST /api/v1/chats - 创建新聊天
		chats.POST("", chatHandler.CreateChat)

		// GET /api/v1/chats/:id - 获取指定聊天信息
		chats.GET("/:id", chatHandler.GetChat)

		// PUT /api/v1/chats/:id - 更新聊天信息（群聊名称、头像等）
		chats.PUT("/:id", chatHandler.UpdateChat)

		// DELETE /api/v1/chats/:id - 删除聊天（离开聊天）
		chats.DELETE("/:id", chatHandler.LeaveChat)

		// 聊天成员管理
		members := chats.Group("/:id/members")
		{
			// GET /api/v1/chats/:id/members - 获取聊天成员列表
			members.GET("", chatHandler.ListChatMembers)

			// POST /api/v1/chats/:id/members - 添加聊天成员
			members.POST("", chatHandler.AddChatMembers)

			// GET /api/v1/chats/:id/members/:user_id - 获取指定成员信息
			members.GET("/:user_id", chatHandler.GetChatMember)

			// PUT /api/v1/chats/:id/members/:user_id - 更新成员信息（角色、昵称等）
			members.PUT("/:user_id", chatHandler.UpdateChatMember)

			// DELETE /api/v1/chats/:id/members/:user_id - 移除聊天成员
			members.DELETE("/:user_id", chatHandler.RemoveChatMember)
		}

		// 聊天消息管理
		messages := chats.Group("/:id/messages")
		{
			// GET /api/v1/chats/:id/messages - 获取聊天消息列表
			messages.GET("", chatHandler.ListChatMessages)

			// POST /api/v1/chats/:id/messages - 发送消息
			messages.POST("", chatHandler.SendMessage)

			// GET /api/v1/chats/:id/messages/:msg_id - 获取指定消息
			messages.GET("/:msg_id", chatHandler.GetMessage)

			// PUT /api/v1/chats/:id/messages/:msg_id - 更新消息（编辑）
			messages.PUT("/:msg_id", chatHandler.UpdateMessage)

			// DELETE /api/v1/chats/:id/messages/:msg_id - 删除消息
			messages.DELETE("/:msg_id", chatHandler.DeleteMessage)

			// POST /api/v1/chats/:id/messages/:msg_id/recall - 撤回消息
			messages.POST("/:msg_id/recall", chatHandler.RecallMessage)
		}

		// 聊天设置
		settings := chats.Group("/:id/settings")
		{
			// GET /api/v1/chats/:id/settings - 获取聊天设置
			settings.GET("", chatHandler.GetChatSettings)

			// PUT /api/v1/chats/:id/settings - 更新聊天设置
			settings.PUT("", chatHandler.UpdateChatSettings)

			// PUT /api/v1/chats/:id/settings/mute - 设置免打扰
			settings.PUT("/mute", chatHandler.MuteChat)

			// DELETE /api/v1/chats/:id/settings/mute - 取消免打扰
			settings.DELETE("/mute", chatHandler.UnmuteChat)
		}
	}
}
