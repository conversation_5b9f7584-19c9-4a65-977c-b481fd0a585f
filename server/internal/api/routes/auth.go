package routes

import (
	"github.com/gin-gonic/gin"

	"liyu/internal/api/handlers"
	"liyu/internal/api/middleware"
)

// SetupAuthRoutes 设置认证相关路由
func SetupAuthRoutes(router *gin.RouterGroup, authHandler *handlers.AuthHandler) {
	auth := router.Group("/auth")
	{
		// POST /api/v1/auth/register - 用户注册
		auth.POST("/register", authHandler.Register)

		// POST /api/v1/auth/login - 用户登录
		auth.POST("/login", authHandler.Login)

		// POST /api/v1/auth/refresh - 刷新令牌
		auth.POST("/refresh", authHandler.RefreshToken)

		// GET /api/v1/auth/validate - 验证令牌（需要认证）
		auth.GET("/validate", middleware.AuthMiddleware(), authHandler.ValidateToken)

		// POST /api/v1/auth/logout - 用户登出（需要认证）
		auth.POST("/logout", middleware.AuthMiddleware(), authHandler.Logout)

		// GET /api/v1/auth/me - 获取当前用户信息（需要认证）
		auth.GET("/me", middleware.AuthMiddleware(), authHandler.GetCurrentUser)
	}
}
