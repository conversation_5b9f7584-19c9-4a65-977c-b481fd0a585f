package database

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"liyu/internal/config"
	"liyu/internal/domain/chat"
	"liyu/internal/domain/message"
	"liyu/internal/domain/user"
)

// DB 全局数据库连接
var DB *gorm.DB

// Init 初始化数据库连接
func Init(cfg *config.Config) error {
	// 配置GORM日志级别
	var logLevel logger.LogLevel
	switch cfg.Database.MySQL.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	default:
		logLevel = logger.Info
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(cfg.GetMySQLDSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的sql.DB对象来配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxIdleConns(cfg.Database.MySQL.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.Database.MySQL.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.Database.MySQL.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	DB = db
	log.Println("数据库连接成功")

	return nil
}

// AutoMigrate 自动迁移数据库表（仅用于开发环境）
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	// 按照依赖关系顺序迁移表
	err := DB.AutoMigrate(
		&user.User{},
		&chat.Chat{},
		&chat.Member{},
		&message.SequenceCounter{},
		&message.Message{},
		&Migration{},
	)
	if err != nil {
		return fmt.Errorf("数据库表迁移失败: %w", err)
	}

	log.Println("数据库表迁移完成")
	return nil
}

// RunMigrations 运行数据库迁移
func RunMigrations(migrationsDir string) error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	manager := NewMigrationManager(DB, migrationsDir)

	// 初始化迁移表
	if err := manager.InitMigrationTable(); err != nil {
		return fmt.Errorf("初始化迁移表失败: %w", err)
	}

	// 应用待执行的迁移
	if err := manager.ApplyPendingMigrations(); err != nil {
		return fmt.Errorf("应用迁移失败: %w", err)
	}

	return nil
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(tx *gorm.DB) error) error {
	return DB.Transaction(fn)
}

// Health 检查数据库健康状态
func Health() error {
	if DB == nil {
		return fmt.Errorf("数据库未初始化")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Ping()
}

// GetStats 获取数据库连接统计信息
func GetStats() map[string]interface{} {
	if DB == nil {
		return map[string]interface{}{
			"status": "not_initialized",
		}
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"status":              "connected",
		"open_connections":    stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}
