package database

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Migration 数据库迁移结构
type Migration struct {
	Version   string    `gorm:"primaryKey" json:"version"`
	AppliedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP" json:"applied_at"`
}

// TableName 指定表名
func (Migration) TableName() string {
	return "schema_migrations"
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db            *gorm.DB
	migrationsDir string
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB, migrationsDir string) *MigrationManager {
	return &MigrationManager{
		db:            db,
		migrationsDir: migrationsDir,
	}
}

// InitMigrationTable 初始化迁移表
func (m *MigrationManager) InitMigrationTable() error {
	return m.db.AutoMigrate(&Migration{})
}

// GetAppliedMigrations 获取已应用的迁移
func (m *MigrationManager) GetAppliedMigrations() ([]string, error) {
	var migrations []Migration
	err := m.db.Order("version").Find(&migrations).Error
	if err != nil {
		return nil, err
	}

	versions := make([]string, len(migrations))
	for i, migration := range migrations {
		versions[i] = migration.Version
	}
	return versions, nil
}

// GetPendingMigrations 获取待应用的迁移
func (m *MigrationManager) GetPendingMigrations() ([]string, error) {
	// 获取已应用的迁移
	applied, err := m.GetAppliedMigrations()
	if err != nil {
		return nil, err
	}

	appliedMap := make(map[string]bool)
	for _, version := range applied {
		appliedMap[version] = true
	}

	// 获取所有迁移文件
	allMigrations, err := m.getAllMigrationFiles()
	if err != nil {
		return nil, err
	}

	// 找出待应用的迁移
	var pending []string
	for _, migration := range allMigrations {
		if !appliedMap[migration] {
			pending = append(pending, migration)
		}
	}

	return pending, nil
}

// getAllMigrationFiles 获取所有迁移文件
func (m *MigrationManager) getAllMigrationFiles() ([]string, error) {
	var migrations []string

	err := filepath.WalkDir(m.migrationsDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() {
			return nil
		}

		if strings.HasSuffix(path, ".sql") {
			// 提取文件名（不包含扩展名）作为版本号
			filename := filepath.Base(path)
			version := strings.TrimSuffix(filename, ".sql")
			migrations = append(migrations, version)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 排序确保按版本顺序执行
	sort.Strings(migrations)
	return migrations, nil
}

// ApplyMigration 应用单个迁移
func (m *MigrationManager) ApplyMigration(version string) error {
	// 检查是否已应用
	var count int64
	err := m.db.Model(&Migration{}).Where("version = ?", version).Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return fmt.Errorf("migration %s already applied", version)
	}

	// 读取SQL文件
	sqlFile := filepath.Join(m.migrationsDir, version+".sql")
	sqlContent, err := os.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("failed to read migration file %s: %w", sqlFile, err)
	}

	// 在事务中执行迁移
	return m.db.Transaction(func(tx *gorm.DB) error {
		// 执行SQL
		if err := tx.Exec(string(sqlContent)).Error; err != nil {
			return fmt.Errorf("failed to execute migration %s: %w", version, err)
		}

		// 记录迁移
		migration := Migration{
			Version:   version,
			AppliedAt: time.Now(),
		}
		if err := tx.Create(&migration).Error; err != nil {
			return fmt.Errorf("failed to record migration %s: %w", version, err)
		}

		return nil
	})
}

// ApplyPendingMigrations 应用所有待执行的迁移
func (m *MigrationManager) ApplyPendingMigrations() error {
	pending, err := m.GetPendingMigrations()
	if err != nil {
		return err
	}

	if len(pending) == 0 {
		fmt.Println("No pending migrations")
		return nil
	}

	fmt.Printf("Applying %d pending migrations...\n", len(pending))

	for _, version := range pending {
		fmt.Printf("Applying migration: %s\n", version)
		if err := m.ApplyMigration(version); err != nil {
			return fmt.Errorf("failed to apply migration %s: %w", version, err)
		}
		fmt.Printf("Migration %s applied successfully\n", version)
	}

	fmt.Println("All migrations applied successfully")
	return nil
}

// GetMigrationStatus 获取迁移状态
func (m *MigrationManager) GetMigrationStatus() (map[string]bool, error) {
	applied, err := m.GetAppliedMigrations()
	if err != nil {
		return nil, err
	}

	all, err := m.getAllMigrationFiles()
	if err != nil {
		return nil, err
	}

	appliedMap := make(map[string]bool)
	for _, version := range applied {
		appliedMap[version] = true
	}

	status := make(map[string]bool)
	for _, version := range all {
		status[version] = appliedMap[version]
	}

	return status, nil
}

// CreateMigration 创建新的迁移文件
func (m *MigrationManager) CreateMigration(name string) (string, error) {
	// 生成版本号（时间戳格式）
	timestamp := time.Now().Format("20060102150405")
	version := fmt.Sprintf("%s_%s", timestamp, name)
	filename := fmt.Sprintf("%s.sql", version)
	filepath := filepath.Join(m.migrationsDir, filename)

	// 创建迁移文件模板
	template := fmt.Sprintf(`-- Migration: %s
-- Description: %s
-- Version: %s
-- Date: %s

-- Add your SQL statements here

`, version, name, "1.0.0", time.Now().Format("2006-01-02"))

	// 确保目录存在
	if err := os.MkdirAll(m.migrationsDir, 0755); err != nil {
		return "", err
	}

	// 写入文件
	if err := os.WriteFile(filepath, []byte(template), 0644); err != nil {
		return "", err
	}

	return filepath, nil
}
