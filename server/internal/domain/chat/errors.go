package chat

import "errors"

// 聊天相关错误定义
var (
	// 会话错误
	ErrChatNotFound           = errors.New("chat not found")
	ErrChatExists             = errors.New("chat already exists")
	ErrInvalidChatType        = errors.New("invalid chat type")
	ErrChatDisabled           = errors.New("chat is disabled")
	ErrChatDeleted            = errors.New("chat is deleted")
	ErrPrivateChatMemberCount = errors.New("private chat must have exactly 2 members")
	ErrGroupChatMemberCount   = errors.New("group chat must have at least 2 members")

	// 成员错误
	ErrNotMember         = errors.New("user is not a member of this chat")
	ErrAlreadyMember     = errors.New("user is already a member of this chat")
	ErrMemberNotFound    = errors.New("member not found")
	ErrMemberExists      = errors.New("member already exists")
	ErrInvalidMemberRole = errors.New("invalid member role")
	ErrMemberMuted       = errors.New("member is muted")

	// 权限错误
	ErrPermissionDenied       = errors.New("permission denied")
	ErrInsufficientPermission = errors.New("insufficient permission")
	ErrNotChatOwner           = errors.New("user is not the chat owner")
	ErrNotChatAdmin           = errors.New("user is not a chat admin")
	ErrCannotRemoveOwner      = errors.New("cannot remove chat owner")
	ErrCannotChangeOwnerRole  = errors.New("cannot change owner role")
	ErrCannotManageOwner      = errors.New("cannot manage chat owner")
	ErrCannotManageAdmin      = errors.New("cannot manage chat admin")
)
