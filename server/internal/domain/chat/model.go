package chat

import (
	"time"

	"gorm.io/gorm"
)

// Chat 会话模型
type Chat struct {
	ID            uint64         `gorm:"primaryKey;autoIncrement;comment:会话ID" json:"id"`
	Type          Type           `gorm:"type:tinyint;not null;index:idx_type;comment:会话类型 (1: 单聊, 2: 群聊)" json:"type"`
	Name          *string        `gorm:"type:varchar(255);comment:会话名称" json:"name"`
	Avatar        *string        `gorm:"type:varchar(255);comment:会话头像" json:"avatar"`
	Description   *string        `gorm:"type:text;comment:会话描述(群聊用)" json:"description"`
	OwnerID       *uint64        `gorm:"index:idx_owner;comment:群主用户ID（仅群聊有效）" json:"owner_id"`
	LastSeq       uint64         `gorm:"not null;default:0;comment:会话中最后一条消息的序号(冗余字段，优化查询)" json:"last_seq"`
	LastMessageID *uint64        `gorm:"comment:最后一条消息ID(冗余字段)" json:"last_message_id"`
	Status        Status         `gorm:"type:tinyint;not null;default:1;comment:会话状态(1:正常,2:禁用,3:删除)" json:"status"`
	CreatedAt     time.Time      `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);index:idx_updated;comment:更新时间" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联（按需加载）
	Members []Member `gorm:"foreignKey:ChatID;references:ID" json:"members,omitempty"`
}

// TableName 指定表名
func (Chat) TableName() string {
	return "chats"
}

// Type 会话类型枚举
type Type int8

const (
	TypePrivate Type = 1 // 单聊
	TypeGroup   Type = 2 // 群聊
)

// String 类型字符串表示
func (t Type) String() string {
	switch t {
	case TypePrivate:
		return "private"
	case TypeGroup:
		return "group"
	default:
		return "unknown"
	}
}

// IsValid 检查类型是否有效
func (t Type) IsValid() bool {
	return t == TypePrivate || t == TypeGroup
}

// Status 会话状态枚举
type Status int8

const (
	StatusNormal   Status = 1 // 正常
	StatusDisabled Status = 2 // 禁用
	StatusDeleted  Status = 3 // 删除
)

// String 状态字符串表示
func (s Status) String() string {
	switch s {
	case StatusNormal:
		return "normal"
	case StatusDisabled:
		return "disabled"
	case StatusDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

// IsValid 检查状态是否有效
func (s Status) IsValid() bool {
	return s >= StatusNormal && s <= StatusDeleted
}

// IsActive 检查会话是否活跃
func (c *Chat) IsActive() bool {
	return c.Status == StatusNormal
}

// IsPrivate 检查是否为私聊
func (c *Chat) IsPrivate() bool {
	return c.Type == TypePrivate
}

// IsGroup 检查是否为群聊
func (c *Chat) IsGroup() bool {
	return c.Type == TypeGroup
}

// GetDisplayName 获取显示名称
func (c *Chat) GetDisplayName() string {
	if c.Name != nil && *c.Name != "" {
		return *c.Name
	}
	if c.IsPrivate() {
		return "私聊"
	}
	return "群聊"
}

// CreateChatRequest 创建会话请求
type CreateChatRequest struct {
	Type        Type     `json:"type" binding:"required,oneof=1 2"`
	Name        *string  `json:"name" binding:"omitempty,max=255"`
	Avatar      *string  `json:"avatar" binding:"omitempty,url"`
	Description *string  `json:"description" binding:"omitempty,max=1000"`
	MemberIDs   []uint64 `json:"member_ids" binding:"required,min=1"`
}

// UpdateChatRequest 更新会话请求
type UpdateChatRequest struct {
	Name        *string `json:"name" binding:"omitempty,max=255"`
	Avatar      *string `json:"avatar" binding:"omitempty,url"`
	Description *string `json:"description" binding:"omitempty,max=1000"`
}

// ChatResponse 会话响应
type ChatResponse struct {
	ID            uint64           `json:"id"`
	Type          Type             `json:"type"`
	Name          *string          `json:"name"`
	Avatar        *string          `json:"avatar"`
	Description   *string          `json:"description"`
	OwnerID       *uint64          `json:"owner_id"`
	LastSeq       uint64           `json:"last_seq"`
	LastMessageID *uint64          `json:"last_message_id"`
	Status        Status           `json:"status"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
	Members       []*MemberResponse `json:"members,omitempty"`
	MemberCount   int              `json:"member_count,omitempty"`
}

// ToResponse 转换为响应格式
func (c *Chat) ToResponse() *ChatResponse {
	resp := &ChatResponse{
		ID:            c.ID,
		Type:          c.Type,
		Name:          c.Name,
		Avatar:        c.Avatar,
		Description:   c.Description,
		OwnerID:       c.OwnerID,
		LastSeq:       c.LastSeq,
		LastMessageID: c.LastMessageID,
		Status:        c.Status,
		CreatedAt:     c.CreatedAt,
		UpdatedAt:     c.UpdatedAt,
	}

	// 转换成员信息
	if len(c.Members) > 0 {
		resp.Members = make([]*MemberResponse, len(c.Members))
		for i, member := range c.Members {
			resp.Members[i] = member.ToResponse()
		}
		resp.MemberCount = len(c.Members)
	}

	return resp
}

// ChatListResponse 会话列表响应
type ChatListResponse struct {
	Chats      []*ChatResponse `json:"chats"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}

// Validate 验证创建会话请求
func (req *CreateChatRequest) Validate() error {
	if !req.Type.IsValid() {
		return ErrInvalidChatType
	}

	if req.Type == TypePrivate && len(req.MemberIDs) != 2 {
		return ErrPrivateChatMemberCount
	}

	if req.Type == TypeGroup && len(req.MemberIDs) < 2 {
		return ErrGroupChatMemberCount
	}

	return nil
}
