package chat

import (
	"context"
	"gorm.io/gorm"
)

// Repository 聊天仓储接口
type Repository interface {
	// Create 创建聊天
	Create(ctx context.Context, chat *Chat) error
	
	// GetByID 根据ID获取聊天
	GetByID(ctx context.Context, id uint64) (*Chat, error)
	
	// GetByIDWithMembers 根据ID获取聊天（包含成员信息）
	GetByIDWithMembers(ctx context.Context, id uint64) (*Chat, error)
	
	// Update 更新聊天
	Update(ctx context.Context, chat *Chat) error
	
	// Delete 删除聊天
	Delete(ctx context.Context, id uint64) error
	
	// GetUserChats 获取用户的聊天列表
	GetUserChats(ctx context.Context, userID uint64, offset, limit int) ([]*Chat, int64, error)
	
	// FindPrivateChat 查找两个用户之间的私聊
	FindPrivateChat(ctx context.Context, userID1, userID2 uint64) (*Chat, error)
	
	// GetChatMembers 获取聊天成员列表
	GetChatMembers(ctx context.Context, chatID uint64) ([]*Member, error)
	
	// AddMember 添加聊天成员
	AddMember(ctx context.Context, member *Member) error
	
	// RemoveMember 移除聊天成员
	RemoveMember(ctx context.Context, chatID, userID uint64) error
	
	// IsMember 检查用户是否为聊天成员
	IsMember(ctx context.Context, chatID, userID uint64) (bool, error)
	
	// GetMember 获取聊天成员信息
	GetMember(ctx context.Context, chatID, userID uint64) (*Member, error)
	
	// UpdateLastMessage 更新聊天的最后消息信息
	UpdateLastMessage(ctx context.Context, chatID uint64, messageID uint64, seq uint64) error
}

// GormRepository GORM实现的聊天仓储
type GormRepository struct {
	db *gorm.DB
}

// NewGormRepository 创建GORM聊天仓储
func NewGormRepository(db *gorm.DB) Repository {
	return &GormRepository{db: db}
}

// Create 创建聊天
func (r *GormRepository) Create(ctx context.Context, chat *Chat) error {
	return r.db.WithContext(ctx).Create(chat).Error
}

// GetByID 根据ID获取聊天
func (r *GormRepository) GetByID(ctx context.Context, id uint64) (*Chat, error) {
	var chat Chat
	err := r.db.WithContext(ctx).First(&chat, id).Error
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// GetByIDWithMembers 根据ID获取聊天（包含成员信息）
func (r *GormRepository) GetByIDWithMembers(ctx context.Context, id uint64) (*Chat, error) {
	var chat Chat
	err := r.db.WithContext(ctx).Preload("Members").First(&chat, id).Error
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// Update 更新聊天
func (r *GormRepository) Update(ctx context.Context, chat *Chat) error {
	return r.db.WithContext(ctx).Save(chat).Error
}

// Delete 删除聊天
func (r *GormRepository) Delete(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Delete(&Chat{}, id).Error
}

// GetUserChats 获取用户的聊天列表
func (r *GormRepository) GetUserChats(ctx context.Context, userID uint64, offset, limit int) ([]*Chat, int64, error) {
	var chats []*Chat
	var total int64
	
	// 通过聊天成员表关联查询用户的聊天
	query := r.db.WithContext(ctx).
		Table("chats").
		Joins("JOIN chat_users ON chats.id = chat_users.chat_id").
		Where("chat_users.user_id = ? AND chats.deleted_at IS NULL", userID).
		Order("chats.updated_at DESC")
	
	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	err = query.Offset(offset).Limit(limit).Find(&chats).Error
	if err != nil {
		return nil, 0, err
	}
	
	return chats, total, nil
}

// FindPrivateChat 查找两个用户之间的私聊
func (r *GormRepository) FindPrivateChat(ctx context.Context, userID1, userID2 uint64) (*Chat, error) {
	var chat Chat
	
	// 查找类型为私聊且包含这两个用户的聊天
	err := r.db.WithContext(ctx).
		Table("chats").
		Joins("JOIN chat_users cu1 ON chats.id = cu1.chat_id AND cu1.user_id = ?", userID1).
		Joins("JOIN chat_users cu2 ON chats.id = cu2.chat_id AND cu2.user_id = ?", userID2).
		Where("chats.type = ? AND chats.deleted_at IS NULL", TypePrivate).
		First(&chat).Error
	
	if err != nil {
		return nil, err
	}
	
	return &chat, nil
}

// GetChatMembers 获取聊天成员列表
func (r *GormRepository) GetChatMembers(ctx context.Context, chatID uint64) ([]*Member, error) {
	var members []*Member
	err := r.db.WithContext(ctx).Where("chat_id = ?", chatID).Find(&members).Error
	return members, err
}

// AddMember 添加聊天成员
func (r *GormRepository) AddMember(ctx context.Context, member *Member) error {
	return r.db.WithContext(ctx).Create(member).Error
}

// RemoveMember 移除聊天成员
func (r *GormRepository) RemoveMember(ctx context.Context, chatID, userID uint64) error {
	return r.db.WithContext(ctx).
		Where("chat_id = ? AND user_id = ?", chatID, userID).
		Delete(&Member{}).Error
}

// IsMember 检查用户是否为聊天成员
func (r *GormRepository) IsMember(ctx context.Context, chatID, userID uint64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&Member{}).
		Where("chat_id = ? AND user_id = ?", chatID, userID).
		Count(&count).Error
	return count > 0, err
}

// GetMember 获取聊天成员信息
func (r *GormRepository) GetMember(ctx context.Context, chatID, userID uint64) (*Member, error) {
	var member Member
	err := r.db.WithContext(ctx).
		Where("chat_id = ? AND user_id = ?", chatID, userID).
		First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

// UpdateLastMessage 更新聊天的最后消息信息
func (r *GormRepository) UpdateLastMessage(ctx context.Context, chatID uint64, messageID uint64, seq uint64) error {
	return r.db.WithContext(ctx).
		Model(&Chat{}).
		Where("id = ?", chatID).
		Updates(map[string]interface{}{
			"last_message_id": messageID,
			"last_seq":        seq,
		}).Error
}
