package chat

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"liyu/internal/domain/message"
)

// Service 聊天服务接口
type Service interface {
	// CreateGroupChat 创建群聊
	CreateGroupChat(ctx context.Context, req *CreateChatRequest, creatorID uint64) (*Chat, error)

	// GetOrCreatePrivateChat 获取或创建私聊（懒创建）
	GetOrCreatePrivateChat(ctx context.Context, userID1, userID2 uint64) (*Chat, error)

	// GetChat 获取聊天信息
	GetChat(ctx context.Context, chatID uint64, userID uint64) (*Chat, error)

	// GetUserChats 获取用户聊天列表
	GetUserChats(ctx context.Context, userID uint64, page, pageSize int) ([]*Chat, int64, error)

	// AddMembers 添加聊天成员
	AddMembers(ctx context.Context, chatID uint64, memberIDs []uint64, operatorID uint64) error

	// RemoveMember 移除聊天成员
	RemoveMember(ctx context.Context, chatID uint64, memberID uint64, operatorID uint64) error

	// IsMember 检查用户是否为聊天成员
	IsMember(ctx context.Context, chatID uint64, userID uint64) (bool, error)

	// GetChatMembers 获取聊天成员列表
	GetChatMembers(ctx context.Context, chatID uint64, userID uint64) ([]*Member, error)

	// UpdateLastMessage 更新聊天的最后消息信息
	UpdateLastMessage(ctx context.Context, chatID uint64, messageID uint64, seq uint64) error
}

// ServiceImpl 聊天服务实现
type ServiceImpl struct {
	chatRepo    Repository
	messageRepo message.Repository
	db          *gorm.DB
}

// NewService 创建聊天服务
func NewService(chatRepo Repository, messageRepo message.Repository, db *gorm.DB) Service {
	return &ServiceImpl{
		chatRepo:    chatRepo,
		messageRepo: messageRepo,
		db:          db,
	}
}

// CreateGroupChat 创建群聊
func (s *ServiceImpl) CreateGroupChat(ctx context.Context, req *CreateChatRequest, creatorID uint64) (*Chat, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	if req.Type != TypeGroup {
		return nil, ErrInvalidChatType
	}

	// 确保创建者在成员列表中
	memberIDs := req.MemberIDs
	creatorInList := false
	for _, id := range memberIDs {
		if id == creatorID {
			creatorInList = true
			break
		}
	}
	if !creatorInList {
		memberIDs = append(memberIDs, creatorID)
	}

	// 开始事务
	return s.createChatWithTransaction(ctx, req.Type, req.Name, req.Avatar, req.Description, &creatorID, memberIDs)
}

// GetOrCreatePrivateChat 获取或创建私聊（懒创建）
func (s *ServiceImpl) GetOrCreatePrivateChat(ctx context.Context, userID1, userID2 uint64) (*Chat, error) {
	if userID1 == userID2 {
		return nil, fmt.Errorf("不能与自己创建私聊")
	}

	// 先尝试查找现有的私聊
	existingChat, err := s.chatRepo.FindPrivateChat(ctx, userID1, userID2)
	if err == nil {
		return existingChat, nil
	}

	// 如果不存在，则创建新的私聊
	if err == gorm.ErrRecordNotFound {
		memberIDs := []uint64{userID1, userID2}
		return s.createChatWithTransaction(ctx, TypePrivate, nil, nil, nil, nil, memberIDs)
	}

	return nil, err
}

// GetChat 获取聊天信息
func (s *ServiceImpl) GetChat(ctx context.Context, chatID uint64, userID uint64) (*Chat, error) {
	// 检查用户是否为聊天成员
	isMember, err := s.chatRepo.IsMember(ctx, chatID, userID)
	if err != nil {
		return nil, err
	}
	if !isMember {
		return nil, ErrNotMember
	}

	return s.chatRepo.GetByIDWithMembers(ctx, chatID)
}

// GetUserChats 获取用户聊天列表
func (s *ServiceImpl) GetUserChats(ctx context.Context, userID uint64, page, pageSize int) ([]*Chat, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	return s.chatRepo.GetUserChats(ctx, userID, offset, pageSize)
}

// AddMembers 添加聊天成员
func (s *ServiceImpl) AddMembers(ctx context.Context, chatID uint64, memberIDs []uint64, operatorID uint64) error {
	// 检查操作者权限
	member, err := s.chatRepo.GetMember(ctx, chatID, operatorID)
	if err != nil {
		return ErrNotMember
	}

	// 获取聊天信息
	chat, err := s.chatRepo.GetByID(ctx, chatID)
	if err != nil {
		return err
	}

	// 私聊不能添加成员
	if chat.Type == TypePrivate {
		return fmt.Errorf("私聊不能添加成员")
	}

	// 检查权限（只有群主和管理员可以添加成员）
	if member.Role != RoleOwner && member.Role != RoleAdmin {
		return ErrPermissionDenied
	}

	// 添加成员
	for _, memberID := range memberIDs {
		// 检查是否已经是成员
		exists, err := s.chatRepo.IsMember(ctx, chatID, memberID)
		if err != nil {
			return err
		}
		if exists {
			continue // 跳过已存在的成员
		}

		// 添加新成员
		newMember := &Member{
			ChatID:   chatID,
			UserID:   memberID,
			Role:     RoleMember,
			JoinedAt: time.Now(),
		}

		if err := s.chatRepo.AddMember(ctx, newMember); err != nil {
			return err
		}
	}

	return nil
}

// RemoveMember 移除聊天成员
func (s *ServiceImpl) RemoveMember(ctx context.Context, chatID uint64, memberID uint64, operatorID uint64) error {
	// 检查操作者权限
	operator, err := s.chatRepo.GetMember(ctx, chatID, operatorID)
	if err != nil {
		return ErrNotMember
	}

	// 获取聊天信息
	chat, err := s.chatRepo.GetByID(ctx, chatID)
	if err != nil {
		return err
	}

	// 私聊不能移除成员
	if chat.Type == TypePrivate {
		return fmt.Errorf("私聊不能移除成员")
	}

	// 获取被移除成员信息
	targetMember, err := s.chatRepo.GetMember(ctx, chatID, memberID)
	if err != nil {
		return ErrNotMember
	}

	// 权限检查
	if operator.Role == RoleMember {
		// 普通成员只能移除自己
		if operatorID != memberID {
			return ErrPermissionDenied
		}
	} else if operator.Role == RoleAdmin {
		// 管理员不能移除群主和其他管理员
		if targetMember.Role == RoleOwner || (targetMember.Role == RoleAdmin && operatorID != memberID) {
			return ErrPermissionDenied
		}
	}
	// 群主可以移除任何人

	return s.chatRepo.RemoveMember(ctx, chatID, memberID)
}

// IsMember 检查用户是否为聊天成员
func (s *ServiceImpl) IsMember(ctx context.Context, chatID uint64, userID uint64) (bool, error) {
	return s.chatRepo.IsMember(ctx, chatID, userID)
}

// GetChatMembers 获取聊天成员列表
func (s *ServiceImpl) GetChatMembers(ctx context.Context, chatID uint64, userID uint64) ([]*Member, error) {
	// 检查用户是否为聊天成员
	isMember, err := s.chatRepo.IsMember(ctx, chatID, userID)
	if err != nil {
		return nil, err
	}
	if !isMember {
		return nil, ErrNotMember
	}

	return s.chatRepo.GetChatMembers(ctx, chatID)
}

// UpdateLastMessage 更新聊天的最后消息信息
func (s *ServiceImpl) UpdateLastMessage(ctx context.Context, chatID uint64, messageID uint64, seq uint64) error {
	return s.chatRepo.UpdateLastMessage(ctx, chatID, messageID, seq)
}

// createChatWithTransaction 在事务中创建聊天
func (s *ServiceImpl) createChatWithTransaction(ctx context.Context, chatType Type, name, avatar, description *string, ownerID *uint64, memberIDs []uint64) (*Chat, error) {
	var result *Chat

	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建聊天
		chat := &Chat{
			Type:        chatType,
			Name:        name,
			Avatar:      avatar,
			Description: description,
			OwnerID:     ownerID,
			Status:      StatusNormal,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := tx.Create(chat).Error; err != nil {
			return err
		}

		// 初始化消息序号计数器
		counter := &message.SequenceCounter{
			ChatID:    chat.ID,
			NextSeq:   1,
			UpdatedAt: time.Now(),
		}
		if err := tx.Create(counter).Error; err != nil {
			return err
		}

		// 添加成员
		for i, memberID := range memberIDs {
			role := RoleMember
			if ownerID != nil && memberID == *ownerID {
				role = RoleOwner
			}

			member := &Member{
				ChatID:   chat.ID,
				UserID:   memberID,
				Role:     role,
				JoinedAt: time.Now(),
			}

			if err := tx.Create(member).Error; err != nil {
				return err
			}

			// 将成员信息添加到聊天对象中
			if i == 0 {
				chat.Members = make([]Member, 0, len(memberIDs))
			}
			chat.Members = append(chat.Members, *member)
		}

		result = chat
		return nil
	})

	return result, err
}
