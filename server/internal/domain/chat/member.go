package chat

import (
	"time"
)

// Member 会话成员模型
type Member struct {
	ID             uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	ChatID         uint64    `gorm:"not null;uniqueIndex:uk_chat_user,priority:1;index:idx_chat_id;comment:会话ID" json:"chat_id"`
	UserID         uint64    `gorm:"not null;uniqueIndex:uk_chat_user,priority:2;index:idx_user_id;comment:用户ID" json:"user_id"`
	Role           Role      `gorm:"type:tinyint;not null;default:3;comment:成员角色 (1:群主,2:管理员,3:普通成员)" json:"role"`
	LastReadSeq    uint64    `gorm:"not null;default:0;index:idx_last_read;comment:用户最后阅读的消息序号" json:"last_read_seq"`
	JoinedAt       time.Time `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);comment:加入时间" json:"joined_at"`
	Muted          bool      `gorm:"not null;default:false;comment:是否被禁言" json:"muted"`
	NicknameInChat *string   `gorm:"type:varchar(50);comment:用户在群内的昵称" json:"nickname_in_chat"`

	// 关联（按需加载）
	Chat *Chat `gorm:"foreignKey:ChatID;references:ID" json:"chat,omitempty"`
	// User 字段需要在使用时手动加载，避免循环依赖
}

// TableName 指定表名
func (Member) TableName() string {
	return "chat_users"
}

// Role 成员角色枚举
type Role int8

const (
	RoleOwner  Role = 1 // 群主
	RoleAdmin  Role = 2 // 管理员
	RoleMember Role = 3 // 普通成员
)

// String 角色字符串表示
func (r Role) String() string {
	switch r {
	case RoleOwner:
		return "owner"
	case RoleAdmin:
		return "admin"
	case RoleMember:
		return "member"
	default:
		return "unknown"
	}
}

// IsValid 检查角色是否有效
func (r Role) IsValid() bool {
	return r >= RoleOwner && r <= RoleMember
}

// IsOwner 检查是否为群主
func (m *Member) IsOwner() bool {
	return m.Role == RoleOwner
}

// IsAdmin 检查是否为管理员
func (m *Member) IsAdmin() bool {
	return m.Role == RoleAdmin
}

// IsMember 检查是否为普通成员
func (m *Member) IsMember() bool {
	return m.Role == RoleMember
}

// CanManage 检查是否有管理权限
func (m *Member) CanManage() bool {
	return m.Role == RoleOwner || m.Role == RoleAdmin
}

// GetDisplayName 获取显示名称
func (m *Member) GetDisplayName() string {
	if m.NicknameInChat != nil && *m.NicknameInChat != "" {
		return *m.NicknameInChat
	}
	// 这里需要从用户信息中获取，实际使用时需要预加载用户信息
	return "用户"
}

// AddMemberRequest 添加成员请求
type AddMemberRequest struct {
	UserIDs []uint64 `json:"user_ids" binding:"required,min=1"`
}

// UpdateMemberRequest 更新成员请求
type UpdateMemberRequest struct {
	Role           *Role   `json:"role" binding:"omitempty,oneof=1 2 3"`
	NicknameInChat *string `json:"nickname_in_chat" binding:"omitempty,max=50"`
	Muted          *bool   `json:"muted"`
}

// MemberResponse 成员响应
type MemberResponse struct {
	ID             uint64    `json:"id"`
	ChatID         uint64    `json:"chat_id"`
	UserID         uint64    `json:"user_id"`
	Role           Role      `json:"role"`
	LastReadSeq    uint64    `json:"last_read_seq"`
	JoinedAt       time.Time `json:"joined_at"`
	Muted          bool      `json:"muted"`
	NicknameInChat *string   `json:"nickname_in_chat"`
	// 用户信息（需要手动填充）
	UserAccount  string  `json:"user_account,omitempty"`
	UserNickname *string `json:"user_nickname,omitempty"`
	UserAvatar   *string `json:"user_avatar,omitempty"`
}

// ToResponse 转换为响应格式
func (m *Member) ToResponse() *MemberResponse {
	return &MemberResponse{
		ID:             m.ID,
		ChatID:         m.ChatID,
		UserID:         m.UserID,
		Role:           m.Role,
		LastReadSeq:    m.LastReadSeq,
		JoinedAt:       m.JoinedAt,
		Muted:          m.Muted,
		NicknameInChat: m.NicknameInChat,
	}
}

// MemberListResponse 成员列表响应
type MemberListResponse struct {
	Members    []*MemberResponse `json:"members"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// Validate 验证更新成员请求
func (req *UpdateMemberRequest) Validate() error {
	if req.Role != nil && !req.Role.IsValid() {
		return ErrInvalidMemberRole
	}
	return nil
}
