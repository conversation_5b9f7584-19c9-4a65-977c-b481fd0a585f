package user

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

// Repository 用户仓储接口
type Repository interface {
	// Create 创建用户
	Create(ctx context.Context, user *User) error
	
	// GetByID 根据ID获取用户
	GetByID(ctx context.Context, id uint64) (*User, error)
	
	// GetByAccount 根据账号获取用户
	GetByAccount(ctx context.Context, account string) (*User, error)
	
	// GetByEmail 根据邮箱获取用户
	GetByEmail(ctx context.Context, email string) (*User, error)
	
	// GetByPhone 根据手机号获取用户
	GetByPhone(ctx context.Context, phone string) (*User, error)
	
	// Update 更新用户
	Update(ctx context.Context, user *User) error
	
	// UpdateFields 更新指定字段
	UpdateFields(ctx context.Context, id uint64, fields map[string]interface{}) error
	
	// Delete 删除用户（软删除）
	Delete(ctx context.Context, id uint64) error
	
	// List 获取用户列表
	List(ctx context.Context, offset, limit int) ([]*User, int64, error)
	
	// ExistsByAccount 检查账号是否存在
	ExistsByAccount(ctx context.Context, account string) (bool, error)
	
	// ExistsByEmail 检查邮箱是否存在
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	
	// ExistsByPhone 检查手机号是否存在
	ExistsByPhone(ctx context.Context, phone string) (bool, error)
}

// GormRepository GORM实现的用户仓储
type GormRepository struct {
	db *gorm.DB
}

// NewGormRepository 创建GORM用户仓储
func NewGormRepository(db *gorm.DB) Repository {
	return &GormRepository{db: db}
}

// Create 创建用户
func (r *GormRepository) Create(ctx context.Context, user *User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// GetByID 根据ID获取用户
func (r *GormRepository) GetByID(ctx context.Context, id uint64) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("id = ? AND status = ?", id, StatusNormal).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, err
	}
	return &user, nil
}

// GetByAccount 根据账号获取用户
func (r *GormRepository) GetByAccount(ctx context.Context, account string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("account = ? AND status = ?", account, StatusNormal).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *GormRepository) GetByEmail(ctx context.Context, email string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("email = ? AND status = ?", email, StatusNormal).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, err
	}
	return &user, nil
}

// GetByPhone 根据手机号获取用户
func (r *GormRepository) GetByPhone(ctx context.Context, phone string) (*User, error) {
	var user User
	err := r.db.WithContext(ctx).Where("phone = ? AND status = ?", phone, StatusNormal).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *GormRepository) Update(ctx context.Context, user *User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

// UpdateFields 更新指定字段
func (r *GormRepository) UpdateFields(ctx context.Context, id uint64, fields map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&User{}).Where("id = ?", id).Updates(fields).Error
}

// Delete 删除用户（软删除）
func (r *GormRepository) Delete(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&User{}).Error
}

// List 获取用户列表
func (r *GormRepository) List(ctx context.Context, offset, limit int) ([]*User, int64, error) {
	var users []*User
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&User{}).Where("status = ?", StatusNormal).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表
	err := r.db.WithContext(ctx).Where("status = ?", StatusNormal).
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&users).Error

	return users, total, err
}

// ExistsByAccount 检查账号是否存在
func (r *GormRepository) ExistsByAccount(ctx context.Context, account string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&User{}).Where("account = ?", account).Count(&count).Error
	return count > 0, err
}

// ExistsByEmail 检查邮箱是否存在
func (r *GormRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	if email == "" {
		return false, nil
	}
	var count int64
	err := r.db.WithContext(ctx).Model(&User{}).Where("email = ?", email).Count(&count).Error
	return count > 0, err
}

// ExistsByPhone 检查手机号是否存在
func (r *GormRepository) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	if phone == "" {
		return false, nil
	}
	var count int64
	err := r.db.WithContext(ctx).Model(&User{}).Where("phone = ?", phone).Count(&count).Error
	return count > 0, err
}
