package user

import "errors"

// 用户相关错误定义
var (
	ErrUserNotFound      = errors.New("user not found")
	ErrUserExists        = errors.New("user already exists")
	ErrAccountExists     = errors.New("account already exists")
	ErrEmailExists       = errors.New("email already exists")
	ErrPhoneExists       = errors.New("phone already exists")
	ErrInvalidPassword   = errors.New("invalid password")
	ErrUserDisabled      = errors.New("user is disabled")
	ErrUserDeleted       = errors.New("user is deleted")
	ErrInvalidUserStatus = errors.New("invalid user status")
)
