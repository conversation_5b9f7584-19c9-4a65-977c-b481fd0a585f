package message

import (
	"context"
	"gorm.io/gorm"
)

// Repository 消息仓储接口
type Repository interface {
	// Create 创建消息
	Create(ctx context.Context, message *Message) error
	
	// GetByID 根据ID获取消息
	GetByID(ctx context.Context, id uint64) (*Message, error)
	
	// GetByChatAndSeq 根据聊天ID和序号获取消息
	GetByChatAndSeq(ctx context.Context, chatID uint64, seq uint64) (*Message, error)
	
	// GetChatMessages 获取聊天消息列表
	GetChatMessages(ctx context.Context, chatID uint64, offset, limit int) ([]*Message, int64, error)
	
	// GetChatMessagesAfterSeq 获取指定序号之后的消息
	GetChatMessagesAfterSeq(ctx context.Context, chatID uint64, afterSeq uint64, limit int) ([]*Message, error)
	
	// Update 更新消息
	Update(ctx context.Context, message *Message) error
	
	// Delete 删除消息
	Delete(ctx context.Context, id uint64) error
	
	// GetSequenceCounter 获取消息序号计数器
	GetSequenceCounter(ctx context.Context, chatID uint64) (*SequenceCounter, error)
	
	// CreateSequenceCounter 创建消息序号计数器
	CreateSequenceCounter(ctx context.Context, counter *SequenceCounter) error
	
	// IncrementSequence 原子性递增序号并返回新序号
	IncrementSequence(ctx context.Context, chatID uint64) (uint64, error)
	
	// CreateMessageWithSequence 在事务中创建消息并更新序号
	CreateMessageWithSequence(ctx context.Context, message *Message) error
}

// GormRepository GORM实现的消息仓储
type GormRepository struct {
	db *gorm.DB
}

// NewGormRepository 创建GORM消息仓储
func NewGormRepository(db *gorm.DB) Repository {
	return &GormRepository{db: db}
}

// Create 创建消息
func (r *GormRepository) Create(ctx context.Context, message *Message) error {
	return r.db.WithContext(ctx).Create(message).Error
}

// GetByID 根据ID获取消息
func (r *GormRepository) GetByID(ctx context.Context, id uint64) (*Message, error) {
	var message Message
	err := r.db.WithContext(ctx).First(&message, id).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// GetByChatAndSeq 根据聊天ID和序号获取消息
func (r *GormRepository) GetByChatAndSeq(ctx context.Context, chatID uint64, seq uint64) (*Message, error) {
	var message Message
	err := r.db.WithContext(ctx).
		Where("chat_id = ? AND seq = ?", chatID, seq).
		First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// GetChatMessages 获取聊天消息列表
func (r *GormRepository) GetChatMessages(ctx context.Context, chatID uint64, offset, limit int) ([]*Message, int64, error) {
	var messages []*Message
	var total int64
	
	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&Message{}).
		Where("chat_id = ? AND status = ?", chatID, StatusNormal).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据，按序号倒序排列
	err = r.db.WithContext(ctx).
		Where("chat_id = ? AND status = ?", chatID, StatusNormal).
		Order("seq DESC").
		Offset(offset).
		Limit(limit).
		Find(&messages).Error
	if err != nil {
		return nil, 0, err
	}
	
	return messages, total, nil
}

// GetChatMessagesAfterSeq 获取指定序号之后的消息
func (r *GormRepository) GetChatMessagesAfterSeq(ctx context.Context, chatID uint64, afterSeq uint64, limit int) ([]*Message, error) {
	var messages []*Message
	
	err := r.db.WithContext(ctx).
		Where("chat_id = ? AND seq > ? AND status = ?", chatID, afterSeq, StatusNormal).
		Order("seq ASC").
		Limit(limit).
		Find(&messages).Error
	
	return messages, err
}

// Update 更新消息
func (r *GormRepository) Update(ctx context.Context, message *Message) error {
	return r.db.WithContext(ctx).Save(message).Error
}

// Delete 删除消息
func (r *GormRepository) Delete(ctx context.Context, id uint64) error {
	return r.db.WithContext(ctx).Delete(&Message{}, id).Error
}

// GetSequenceCounter 获取消息序号计数器
func (r *GormRepository) GetSequenceCounter(ctx context.Context, chatID uint64) (*SequenceCounter, error) {
	var counter SequenceCounter
	err := r.db.WithContext(ctx).First(&counter, "chat_id = ?", chatID).Error
	if err != nil {
		return nil, err
	}
	return &counter, nil
}

// CreateSequenceCounter 创建消息序号计数器
func (r *GormRepository) CreateSequenceCounter(ctx context.Context, counter *SequenceCounter) error {
	return r.db.WithContext(ctx).Create(counter).Error
}

// IncrementSequence 原子性递增序号并返回新序号
func (r *GormRepository) IncrementSequence(ctx context.Context, chatID uint64) (uint64, error) {
	var newSeq uint64
	
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用行锁获取当前计数器
		var counter SequenceCounter
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			First(&counter, "chat_id = ?", chatID).Error
		if err != nil {
			return err
		}
		
		// 递增序号
		newSeq = counter.IncrementSeq()
		
		// 更新计数器
		return tx.Save(&counter).Error
	})
	
	return newSeq, err
}

// CreateMessageWithSequence 在事务中创建消息并更新序号
func (r *GormRepository) CreateMessageWithSequence(ctx context.Context, message *Message) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取并递增序号
		var counter SequenceCounter
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			First(&counter, "chat_id = ?", message.ChatID).Error
		if err != nil {
			return err
		}
		
		// 设置消息序号
		message.Seq = counter.IncrementSeq()
		
		// 创建消息
		if err := tx.Create(message).Error; err != nil {
			return err
		}
		
		// 更新序号计数器
		return tx.Save(&counter).Error
	})
}
