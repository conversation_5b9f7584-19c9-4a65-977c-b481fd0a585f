package message

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Service 消息服务接口
type Service interface {
	// SendMessage 发送消息
	SendMessage(ctx context.Context, req *SendMessageRequest, senderID uint64) (*Message, error)
	
	// GetMessage 获取消息
	GetMessage(ctx context.Context, messageID uint64, userID uint64) (*Message, error)
	
	// GetChatMessages 获取聊天消息列表
	GetChatMessages(ctx context.Context, chatID uint64, userID uint64, page, pageSize int) ([]*Message, int64, error)
	
	// GetNewMessages 获取新消息（用于消息同步）
	GetNewMessages(ctx context.Context, chatID uint64, userID uint64, afterSeq uint64, limit int) ([]*Message, error)
	
	// RecallMessage 撤回消息
	RecallMessage(ctx context.Context, messageID uint64, userID uint64) error
	
	// UpdateMessage 更新消息（编辑）
	UpdateMessage(ctx context.Context, messageID uint64, userID uint64, req *UpdateMessageRequest) (*Message, error)
}

// ServiceImpl 消息服务实现
type ServiceImpl struct {
	messageRepo Repository
	db          *gorm.DB
}

// NewService 创建消息服务
func NewService(messageRepo Repository, db *gorm.DB) Service {
	return &ServiceImpl{
		messageRepo: messageRepo,
		db:          db,
	}
}

// SendMessage 发送消息
func (s *ServiceImpl) SendMessage(ctx context.Context, req *SendMessageRequest, senderID uint64) (*Message, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}
	
	// 创建消息对象
	message := &Message{
		ChatID:       req.ChatID,
		SenderID:     senderID,
		Type:         req.Type,
		Content:      req.Content,
		ContentType:  req.ContentType,
		Status:       StatusNormal,
		ReplyToMsgID: req.ReplyToMsgID,
		Extra:        req.Extra,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	
	// 如果没有指定消息类型，默认为普通消息
	if message.Type == 0 {
		message.Type = TypeNormal
	}
	
	// 验证回复消息是否存在
	if req.ReplyToMsgID != nil {
		replyMsg, err := s.messageRepo.GetByID(ctx, *req.ReplyToMsgID)
		if err != nil {
			return nil, fmt.Errorf("回复的消息不存在")
		}
		if replyMsg.ChatID != req.ChatID {
			return nil, fmt.Errorf("回复的消息不在当前聊天中")
		}
	}
	
	// 在事务中创建消息并更新序号
	err := s.messageRepo.CreateMessageWithSequence(ctx, message)
	if err != nil {
		return nil, err
	}
	
	return message, nil
}

// GetMessage 获取消息
func (s *ServiceImpl) GetMessage(ctx context.Context, messageID uint64, userID uint64) (*Message, error) {
	message, err := s.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return nil, err
	}
	
	// TODO: 检查用户是否有权限查看该消息（是否为聊天成员）
	// 这里需要注入聊天服务来检查权限，为了简化暂时跳过
	
	return message, nil
}

// GetChatMessages 获取聊天消息列表
func (s *ServiceImpl) GetChatMessages(ctx context.Context, chatID uint64, userID uint64, page, pageSize int) ([]*Message, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	// TODO: 检查用户是否为聊天成员
	// 这里需要注入聊天服务来检查权限，为了简化暂时跳过
	
	offset := (page - 1) * pageSize
	return s.messageRepo.GetChatMessages(ctx, chatID, offset, pageSize)
}

// GetNewMessages 获取新消息（用于消息同步）
func (s *ServiceImpl) GetNewMessages(ctx context.Context, chatID uint64, userID uint64, afterSeq uint64, limit int) ([]*Message, error) {
	if limit < 1 || limit > 100 {
		limit = 50
	}
	
	// TODO: 检查用户是否为聊天成员
	// 这里需要注入聊天服务来检查权限，为了简化暂时跳过
	
	return s.messageRepo.GetChatMessagesAfterSeq(ctx, chatID, afterSeq, limit)
}

// RecallMessage 撤回消息
func (s *ServiceImpl) RecallMessage(ctx context.Context, messageID uint64, userID uint64) error {
	// 获取消息
	message, err := s.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return err
	}
	
	// 检查权限：只有发送者可以撤回消息
	if message.SenderID != userID {
		return fmt.Errorf("只能撤回自己发送的消息")
	}
	
	// 检查时间限制：只能撤回2分钟内的消息
	if time.Since(message.CreatedAt) > 2*time.Minute {
		return fmt.Errorf("只能撤回2分钟内的消息")
	}
	
	// 更新消息状态为撤回
	message.Status = StatusRecalled
	message.UpdatedAt = time.Now()
	
	return s.messageRepo.Update(ctx, message)
}

// UpdateMessage 更新消息（编辑）
func (s *ServiceImpl) UpdateMessage(ctx context.Context, messageID uint64, userID uint64, req *UpdateMessageRequest) (*Message, error) {
	// 获取消息
	message, err := s.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return nil, err
	}
	
	// 检查权限：只有发送者可以编辑消息
	if message.SenderID != userID {
		return nil, fmt.Errorf("只能编辑自己发送的消息")
	}
	
	// 检查消息状态：只能编辑正常状态的消息
	if message.Status != StatusNormal {
		return nil, fmt.Errorf("只能编辑正常状态的消息")
	}
	
	// 检查时间限制：只能编辑5分钟内的消息
	if time.Since(message.CreatedAt) > 5*time.Minute {
		return nil, fmt.Errorf("只能编辑5分钟内的消息")
	}
	
	// 更新消息内容
	if req.Content != nil {
		message.Content = req.Content
	}
	if req.Extra != nil {
		message.Extra = req.Extra
	}
	message.UpdatedAt = time.Now()
	
	err = s.messageRepo.Update(ctx, message)
	if err != nil {
		return nil, err
	}
	
	return message, nil
}
