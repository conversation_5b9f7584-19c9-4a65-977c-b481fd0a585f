package message

import (
	"time"
)

// Message 消息模型
type Message struct {
	ID           uint64      `gorm:"primaryKey;autoIncrement;comment:全局唯一消息ID" json:"id"`
	ChatID       uint64      `gorm:"not null;uniqueIndex:uk_chat_seq,priority:1;index:idx_chat_created,priority:1;comment:会话ID" json:"chat_id"`
	Seq          uint64      `gorm:"not null;uniqueIndex:uk_chat_seq,priority:2;index:idx_seq;comment:在会话内的严格递增序号" json:"seq"`
	SenderID     uint64      `gorm:"not null;index:idx_sender;comment:发送者ID" json:"sender_id"`
	Type         Type        `gorm:"type:tinyint;not null;comment:消息类型(1:普通消息,2:广播消息)" json:"type"`
	Content      *string     `gorm:"type:text;comment:消息内容(文本内容或媒体URL)" json:"content"`
	ContentType  ContentType `gorm:"type:tinyint;not null;default:1;comment:内容类型(1:文本,2:图片,3:文件,4:语音,5:视频)" json:"content_type"`
	Status       Status      `gorm:"type:tinyint;not null;default:1;comment:消息状态(1:正常,2:撤回)" json:"status"`
	ReplyToMsgID *uint64     `gorm:"comment:回复的消息ID" json:"reply_to_msg_id"`
	CreatedAt    time.Time   `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3);index:idx_chat_created,priority:2;index:idx_created;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time   `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3)" json:"updated_at"`
	Extra        *string     `gorm:"type:json;comment:扩展字段" json:"extra"`

	// 关联（按需加载）
	ReplyToMsg *Message `gorm:"foreignKey:ReplyToMsgID;references:ID" json:"reply_to_msg,omitempty"`
}

// TableName 指定表名
func (Message) TableName() string {
	return "messages"
}

// Type 消息类型枚举
type Type int8

const (
	TypeNormal    Type = 1 // 普通消息
	TypeBroadcast Type = 2 // 广播消息
)

// String 类型字符串表示
func (t Type) String() string {
	switch t {
	case TypeNormal:
		return "normal"
	case TypeBroadcast:
		return "broadcast"
	default:
		return "unknown"
	}
}

// IsValid 检查类型是否有效
func (t Type) IsValid() bool {
	return t == TypeNormal || t == TypeBroadcast
}

// ContentType 内容类型枚举
type ContentType int8

const (
	ContentTypeText  ContentType = 1 // 文本
	ContentTypeImage ContentType = 2 // 图片
	ContentTypeFile  ContentType = 3 // 文件
	ContentTypeAudio ContentType = 4 // 语音
	ContentTypeVideo ContentType = 5 // 视频
)

// String 内容类型字符串表示
func (ct ContentType) String() string {
	switch ct {
	case ContentTypeText:
		return "text"
	case ContentTypeImage:
		return "image"
	case ContentTypeFile:
		return "file"
	case ContentTypeAudio:
		return "audio"
	case ContentTypeVideo:
		return "video"
	default:
		return "unknown"
	}
}

// IsValid 检查内容类型是否有效
func (ct ContentType) IsValid() bool {
	return ct >= ContentTypeText && ct <= ContentTypeVideo
}

// Status 消息状态枚举
type Status int8

const (
	StatusNormal   Status = 1 // 正常
	StatusRecalled Status = 2 // 撤回
)

// String 状态字符串表示
func (s Status) String() string {
	switch s {
	case StatusNormal:
		return "normal"
	case StatusRecalled:
		return "recalled"
	default:
		return "unknown"
	}
}

// IsValid 检查状态是否有效
func (s Status) IsValid() bool {
	return s == StatusNormal || s == StatusRecalled
}

// IsNormal 检查消息是否正常
func (m *Message) IsNormal() bool {
	return m.Status == StatusNormal
}

// IsRecalled 检查消息是否已撤回
func (m *Message) IsRecalled() bool {
	return m.Status == StatusRecalled
}

// IsReply 检查是否为回复消息
func (m *Message) IsReply() bool {
	return m.ReplyToMsgID != nil
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	ChatID       uint64      `json:"chat_id" binding:"required"`
	Type         Type        `json:"type" binding:"omitempty,oneof=1 2"`
	Content      *string     `json:"content" binding:"required"`
	ContentType  ContentType `json:"content_type" binding:"required,oneof=1 2 3 4 5"`
	ReplyToMsgID *uint64     `json:"reply_to_msg_id"`
	Extra        *string     `json:"extra"`
}

// UpdateMessageRequest 更新消息请求
type UpdateMessageRequest struct {
	Content *string `json:"content" binding:"omitempty"`
	Extra   *string `json:"extra"`
}

// MessageResponse 消息响应
type MessageResponse struct {
	ID           uint64      `json:"id"`
	ChatID       uint64      `json:"chat_id"`
	Seq          uint64      `json:"seq"`
	SenderID     uint64      `json:"sender_id"`
	Type         Type        `json:"type"`
	Content      *string     `json:"content"`
	ContentType  ContentType `json:"content_type"`
	Status       Status      `json:"status"`
	ReplyToMsgID *uint64     `json:"reply_to_msg_id"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
	Extra        *string     `json:"extra"`
	
	// 发送者信息（需要手动填充）
	SenderAccount  string  `json:"sender_account,omitempty"`
	SenderNickname *string `json:"sender_nickname,omitempty"`
	SenderAvatar   *string `json:"sender_avatar,omitempty"`
	
	// 回复消息信息（需要手动填充）
	ReplyToMsg *MessageResponse `json:"reply_to_msg,omitempty"`
}

// ToResponse 转换为响应格式
func (m *Message) ToResponse() *MessageResponse {
	resp := &MessageResponse{
		ID:           m.ID,
		ChatID:       m.ChatID,
		Seq:          m.Seq,
		SenderID:     m.SenderID,
		Type:         m.Type,
		Content:      m.Content,
		ContentType:  m.ContentType,
		Status:       m.Status,
		ReplyToMsgID: m.ReplyToMsgID,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
		Extra:        m.Extra,
	}

	// 转换回复消息
	if m.ReplyToMsg != nil {
		resp.ReplyToMsg = m.ReplyToMsg.ToResponse()
	}

	return resp
}

// MessageListResponse 消息列表响应
type MessageListResponse struct {
	Messages   []*MessageResponse `json:"messages"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
	HasMore    bool               `json:"has_more"`
}

// Validate 验证发送消息请求
func (req *SendMessageRequest) Validate() error {
	if req.Type != 0 && !req.Type.IsValid() {
		return ErrInvalidMessageType
	}
	
	if !req.ContentType.IsValid() {
		return ErrInvalidContentType
	}

	if req.Content == nil || *req.Content == "" {
		return ErrEmptyContent
	}

	return nil
}
