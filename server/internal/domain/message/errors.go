package message

import "errors"

// 消息相关错误定义
var (
	// 消息错误
	ErrMessageNotFound     = errors.New("message not found")
	ErrInvalidMessageType  = errors.New("invalid message type")
	ErrInvalidContentType  = errors.New("invalid content type")
	ErrEmptyContent        = errors.New("message content cannot be empty")
	ErrMessageTooLong      = errors.New("message content too long")
	ErrMessageRecalled     = errors.New("message has been recalled")
	ErrCannotRecallMessage = errors.New("cannot recall this message")
	ErrRecallTimeExpired   = errors.New("recall time has expired")

	// 序号错误
	ErrSequenceNotFound    = errors.New("sequence counter not found")
	ErrInvalidSequence     = errors.New("invalid message sequence")

	// 权限错误
	ErrNotMessageSender    = errors.New("user is not the message sender")
	ErrCannotEditMessage   = errors.New("cannot edit this message")
	ErrCannotDeleteMessage = errors.New("cannot delete this message")
)
