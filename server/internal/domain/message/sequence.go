package message

import (
	"time"
)

// SequenceCounter 消息序号计数器模型
type SequenceCounter struct {
	ChatID    uint64    `gorm:"primaryKey;comment:会话ID" json:"chat_id"`
	NextSeq   uint64    `gorm:"not null;default:1;comment:下一条消息的序号" json:"next_seq"`
	UpdatedAt time.Time `gorm:"type:timestamp(3);not null;default:CURRENT_TIMESTAMP(3)" json:"updated_at"`
}

// TableName 指定表名
func (SequenceCounter) TableName() string {
	return "message_sequence_counters"
}

// GetCurrentSeq 获取当前序号
func (sc *SequenceCounter) GetCurrentSeq() uint64 {
	if sc.NextSeq == 0 {
		return 0
	}
	return sc.NextSeq - 1
}

// IncrementSeq 递增序号并返回新序号
func (sc *SequenceCounter) IncrementSeq() uint64 {
	currentSeq := sc.NextSeq
	sc.NextSeq++
	sc.UpdatedAt = time.Now()
	return currentSeq
}
