# 即时通讯服务器

基于 Go 和 Gin 框架开发的现代化即时通讯服务器，采用领域驱动设计（DDD）架构，支持 HTTP/HTTPS 和 WebSocket/WSS 协议。

## 🏗️ 项目架构

本项目采用清洁架构（Clean Architecture）和领域驱动设计（DDD）原则，代码结构清晰，易于维护和扩展。

### 目录结构

```
server/
├── cmd/                    # 应用程序入口
│   └── server/
│       └── main.go         # 主程序入口
├── internal/               # 内部代码（不对外暴露）
│   ├── api/                # API层
│   │   ├── handlers/       # HTTP处理器
│   │   ├── middleware/     # 中间件
│   │   └── routes/         # 路由定义
│   ├── auth/               # 认证模块
│   ├── config/             # 配置管理
│   ├── database/           # 数据库连接和迁移
│   ├── domain/             # 领域层
│   │   ├── user/           # 用户领域
│   │   ├── chat/           # 聊天领域
│   │   └── message/        # 消息领域
│   ├── websocket/          # WebSocket管理
│   └── common/             # 通用工具
│       └── response/       # 统一响应格式
├── migrations/             # 数据库迁移文件
├── static/                 # 静态文件
├── uploads/                # 上传文件存储
├── config.example.yaml     # 配置文件示例
├── .gitignore             # Git忽略文件
├── go.mod                 # Go模块文件
├── Makefile               # 构建脚本
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Git

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd server
   ```

2. **安装依赖**
   ```bash
   go mod tidy
   ```

3. **配置数据库**
   ```sql
   CREATE DATABASE liyu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

4. **配置应用**
   ```bash
   cp config.example.yaml config.yaml
   # 编辑 config.yaml 修改数据库连接信息
   ```

5. **安装应用**
   ```bash
   go run cmd/server/main.go -install
   ```
   这将创建数据库表并创建管理员用户（账号：admin，密码：123456）

6. **启动服务器**
   ```bash
   go run cmd/server/main.go
   ```

服务器将启动两个端口：
- HTTP 服务器：http://localhost:8080
- WebSocket 服务器：ws://localhost:8081

### 命令行工具

```bash
# 显示版本信息
go run cmd/server/main.go -version

# 安装应用
go run cmd/server/main.go -install

# 运行数据库迁移
go run cmd/server/main.go -migrate

# 启动服务器
go run cmd/server/main.go
```

## 📡 API 接口

### 认证接口

#### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "account": "testuser",
  "password": "123456",
  "nickname": "测试用户"
}
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "account": "testuser",
  "password": "123456"
}
```

#### 刷新令牌
```
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token"
}
```

### 用户接口

#### 获取用户资料
```
GET /api/v1/users/profile
Authorization: Bearer your_access_token
```

#### 更新用户资料
```
PUT /api/v1/users/profile
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "nickname": "新昵称",
  "email": "<EMAIL>"
}
```

### 系统接口

#### 健康检查
```
GET /api/v1/health
```

#### 服务器状态
```
GET /api/v1/status
```

### 安装接口

#### 检查安装状态
```
GET /api/v1/install/status
```

#### 执行应用安装
```
POST /api/v1/install
```

#### 获取安装信息
```
GET /api/v1/install/info
```

## 🔌 WebSocket 连接

### 连接地址
```
ws://localhost:8081/api/ws
```

### 消息格式

所有 WebSocket 消息都使用 JSON 格式：

```json
{
  "type": "message_type",
  "data": {},
  "timestamp": **********,
  "message_id": "uuid"
}
```

### 认证流程

1. 建立 WebSocket 连接
2. 发送认证消息：

```json
{
  "type": "auth",
  "data": {
    "token": "your_access_token",
    "device_id": "device_unique_id"
  }
}
```

3. 服务器返回认证结果：

```json
{
  "type": "auth_success",
  "data": {
    "user_id": 123,
    "device_id": "device_unique_id"
  }
}
```

### 心跳机制

客户端需要定期发送心跳消息：

```json
{
  "type": "heartbeat",
  "data": {
    "timestamp": **********
  }
}
```

服务器会回复：

```json
{
  "type": "heartbeat_response",
  "data": {
    "timestamp": **********
  }
}
```

## 配置说明

### 服务器配置
- `server.http`: HTTP 服务器配置
- `server.websocket`: WebSocket 服务器配置
- `server.*.enable_ssl`: 是否启用 SSL/TLS

### 数据库配置
- `database.mysql`: MySQL 数据库连接配置
- 支持连接池配置

### JWT 配置
- `jwt.secret`: JWT 签名密钥
- `jwt.expires_hours`: 访问令牌过期时间
- `jwt.refresh_expires_hours`: 刷新令牌过期时间

### 日志配置
- `log.level`: 日志级别
- `log.format`: 日志格式（json/text）
- `log.output`: 日志输出（stdout/file）

## 开发说明

### 添加新的 API 接口

1. 在 `handlers/` 目录下创建处理器函数
2. 在 `routes/routes.go` 中添加路由
3. 如需认证，使用 `middleware.AuthMiddleware()`

### 添加新的 WebSocket 消息类型

1. 在 `websocket/handler.go` 的 `handleMessage` 函数中添加新的消息类型
2. 实现对应的处理函数

### 数据库操作

使用 GORM 进行数据库操作，相关函数在 `database/database.go` 中。

## 部署

### 生产环境配置

1. 修改 `config.yaml` 中的配置：
   - 设置 `development.enabled: false`
   - 配置 SSL 证书
   - 设置合适的日志级别

2. 编译程序：
```bash
go build -o liyu-server main.go
```

3. 运行：
```bash
./liyu-server config.yaml
```

### Docker 部署

可以创建 Dockerfile 进行容器化部署。

## 许可证

MIT License
