<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即时通讯测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #fff3e0;
        }
        .message.system {
            background-color: #f3e5f5;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>即时通讯测试客户端</h1>
    
    <!-- 登录区域 -->
    <div class="container">
        <h3>用户登录</h3>
        <div class="form-group">
            <label for="account">用户名:</label>
            <input type="text" id="account" value="testuser">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456">
        </div>
        <button onclick="login()">登录</button>
        <button onclick="register()">注册</button>
    </div>

    <!-- WebSocket连接区域 -->
    <div class="container">
        <h3>WebSocket连接</h3>
        <div id="connectionStatus" class="status disconnected">未连接</div>
        <div class="form-group">
            <label for="wsUrl">WebSocket地址:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8081/ws">
        </div>
        <div class="form-group">
            <label for="deviceId">设备ID:</label>
            <input type="text" id="deviceId" value="web_client_001">
        </div>
        <button id="connectBtn" onclick="connectWebSocket()">连接</button>
        <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>断开连接</button>
    </div>

    <!-- 消息区域 -->
    <div class="container">
        <h3>消息</h3>
        <div id="messages" class="messages"></div>
        <div class="form-group">
            <label for="messageInput">发送消息:</label>
            <textarea id="messageInput" rows="3" placeholder="输入消息内容..."></textarea>
        </div>
        <button onclick="sendMessage()" disabled id="sendBtn">发送消息</button>
        <button onclick="sendHeartbeat()" disabled id="heartbeatBtn">发送心跳</button>
    </div>

    <script>
        let ws = null;
        let accessToken = null;
        let isConnected = false;

        // 登录
        async function login() {
            const account = document.getElementById('account').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('http://localhost:8080/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ account, password })
                });

                const result = await response.json();
                if (response.ok) {
                    accessToken = result.data.access_token;
                    addMessage('system', `登录成功! 用户: ${result.data.user.account}`);
                } else {
                    addMessage('system', `登录失败: ${result.error.message}`);
                }
            } catch (error) {
                addMessage('system', `登录错误: ${error.message}`);
            }
        }

        // 注册
        async function register() {
            const account = document.getElementById('account').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('http://localhost:8080/api/v1/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        account, 
                        password,
                        nickname: account + '_nickname'
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    accessToken = result.data.access_token;
                    addMessage('system', `注册成功! 用户: ${result.data.user.account}`);
                } else {
                    addMessage('system', `注册失败: ${result.error.message}`);
                }
            } catch (error) {
                addMessage('system', `注册错误: ${error.message}`);
            }
        }

        // 连接WebSocket
        function connectWebSocket() {
            if (!accessToken) {
                addMessage('system', '请先登录获取访问令牌');
                return;
            }

            const wsUrl = document.getElementById('wsUrl').value;
            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                updateConnectionStatus(true);
                addMessage('system', 'WebSocket连接已建立');
                
                // 发送认证消息
                const authMessage = {
                    type: 'auth',
                    data: {
                        token: accessToken,
                        device_id: document.getElementById('deviceId').value
                    }
                };
                ws.send(JSON.stringify(authMessage));
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    addMessage('received', `收到消息: ${message.type} - ${JSON.stringify(message.data)}`);
                } catch (error) {
                    addMessage('received', `收到原始消息: ${event.data}`);
                }
            };

            ws.onclose = function() {
                updateConnectionStatus(false);
                addMessage('system', 'WebSocket连接已关闭');
            };

            ws.onerror = function(error) {
                addMessage('system', `WebSocket错误: ${error}`);
            };
        }

        // 断开WebSocket连接
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        // 发送消息
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();
            
            if (!content) {
                addMessage('system', '消息内容不能为空');
                return;
            }

            if (!isConnected) {
                addMessage('system', '请先连接WebSocket');
                return;
            }

            const message = {
                type: 'send_message',
                data: {
                    content: content,
                    content_type: 1,
                    chat_id: 1 // 测试用的会话ID
                }
            };

            ws.send(JSON.stringify(message));
            addMessage('sent', `发送消息: ${content}`);
            messageInput.value = '';
        }

        // 发送心跳
        function sendHeartbeat() {
            if (!isConnected) {
                addMessage('system', '请先连接WebSocket');
                return;
            }

            const message = {
                type: 'heartbeat',
                data: {
                    timestamp: Math.floor(Date.now() / 1000)
                }
            };

            ws.send(JSON.stringify(message));
            addMessage('sent', '发送心跳');
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            const heartbeatBtn = document.getElementById('heartbeatBtn');

            if (connected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                heartbeatBtn.disabled = false;
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                heartbeatBtn.disabled = true;
            }
        }

        // 添加消息到显示区域
        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${content}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
