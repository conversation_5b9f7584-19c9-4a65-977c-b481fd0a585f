-- 初始化数据库脚本
-- 版本: 1.0.0
-- 创建时间: 2024-08-21

-- 用户表 (users)
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `account` VARCHAR(50) NOT NULL COMMENT '用户名，用于登录或显示',
  `password` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
  `nickname` VARCHAR(50) DEFAULT NOT NULL COMMENT '用户昵称',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
  `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '账号状态(0:删除,1:正常,2:禁用)',
  `online_status` TINYINT NOT NULL DEFAULT 0 COMMENT '在线状态(0:离线,1:在线,2:忙碌,3:离开)'
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `deleted_at` TIMESTAMP(3) NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account` (`account`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
  KEY `idx_online_status` (`online_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 会话表 (chats)
CREATE TABLE `chats` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `type` TINYINT NOT NULL COMMENT '会话类型 (1: 单聊, 2: 群聊)',
  `name` VARCHAR(255) DEFAULT NULL COMMENT '会话名称',
  `avatar` VARCHAR(255) DEFAULT NULL COMMENT '会话头像',
  `description` TEXT DEFAULT NULL COMMENT '会话描述(群聊用)',
  `owner_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '群主用户ID（仅群聊有效）',
  `last_seq` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '会话中最后一条消息的序号(冗余字段，优化查询)',
  `last_message_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '最后一条消息ID(冗余字段)',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '会话状态(1:正常,2:禁用,3:删除)',
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_owner` (`owner_id`),
  KEY `idx_updated` (`updated_at`),
  CONSTRAINT `fk_chats_owner` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- 会话成员表 (chat_users)
CREATE TABLE `chat_users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `chat_id` BIGINT UNSIGNED NOT NULL COMMENT '会话ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `role` TINYINT NOT NULL DEFAULT 3 COMMENT '成员角色 (1:群主,2:管理员,3:普通成员)',
  `last_read_seq` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户最后阅读的消息序号',
  `joined_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '加入时间',
  `muted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否被禁言',
  `nickname_in_chat` VARCHAR(50) DEFAULT NULL COMMENT '用户在群内的昵称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chat_user` (`chat_id`, `user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_chat_id` (`chat_id`),
  KEY `idx_last_read` (`last_read_seq`),
  CONSTRAINT `fk_chat_users_chat` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_chat_users_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话成员表';

-- 消息序号计数器表 (message_sequence_counters)
CREATE TABLE `message_sequence_counters` (
  `chat_id` BIGINT UNSIGNED NOT NULL PRIMARY KEY COMMENT '会话ID',
  `next_seq` BIGINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '下一条消息的序号',
  `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  CONSTRAINT `fk_counters_chat` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息序号计数器表';

-- 消息表 (messages)
CREATE TABLE `messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '全局唯一消息ID',
  `chat_id` BIGINT UNSIGNED NOT NULL COMMENT '会话ID',
  `seq` BIGINT UNSIGNED NOT NULL COMMENT '在会话内的严格递增序号',
  `sender_id` BIGINT UNSIGNED NOT NULL COMMENT '发送者ID',
  `type` TINYINT NOT NULL COMMENT '消息类型(1:普通消息,2:广播消息)',
  `content` TEXT COMMENT '消息内容(文本内容或媒体URL)',
  `content_type` TINYINT NOT NULL DEFAULT 1 COMMENT '内容类型(1:文本,2:图片,3:文件,4:语音,5:视频,6:URI,7:)',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '消息状态(1:正常,2:撤回)',
  `reply_to_msg_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '回复的消息ID',
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `extra` JSON DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chat_seq` (`chat_id`, `seq`),
  KEY `idx_chat_created` (`chat_id`, `created_at`),
  KEY `idx_sender` (`sender_id`),
  KEY `idx_seq` (`seq`),
  KEY `idx_created` (`created_at`),
  CONSTRAINT `fk_messages_chat` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_messages_reply` FOREIGN KEY (`reply_to_msg_id`) REFERENCES `messages` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 应用版本表 (app_versions)
CREATE TABLE `app_versions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `version` VARCHAR(20) NOT NULL COMMENT '版本号',
  `applied_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '应用时间',
  PRIMARY KEY (`id`),
  KEY `idx_version` (`version`),
  KEY `idx_applied_at` (`applied_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用版本表';
